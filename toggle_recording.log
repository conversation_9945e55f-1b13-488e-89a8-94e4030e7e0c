2025-07-23 17:48:34,315 - INFO - Starting recording toggle to 'On' for load test cameras
2025-07-23 17:48:34,315 - INFO - Attempting login (attempt 1)
2025-07-23 17:48:34,495 - INFO - Login successful! Token expires in 3600 seconds
2025-07-23 17:48:34,496 - INFO - Fetching all cameras (attempt 1)
2025-07-23 17:48:34,574 - INFO - Successfully fetched 10 cameras
2025-07-23 17:48:34,575 - INFO - Found 10 load test cameras out of 10 total cameras
2025-07-23 17:48:34,575 - INFO - Processing camera 1/10: load-test-camera-loadtest-agent-010-005 (current: Off)
2025-07-23 17:48:34,575 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-005' recording to 'On' (attempt 1)
2025-07-23 17:48:34,608 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-005' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/346e4d75-4a94-43b9-ad17-13fc7bf26f3d
2025-07-23 17:48:35,609 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-005' recording to 'On' (attempt 2)
2025-07-23 17:48:35,646 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-005' (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/346e4d75-4a94-43b9-ad17-13fc7bf26f3d
2025-07-23 17:48:37,648 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-005' recording to 'On' (attempt 3)
2025-07-23 17:48:37,682 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-005' (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/346e4d75-4a94-43b9-ad17-13fc7bf26f3d
2025-07-23 17:48:37,683 - ERROR - Failed to update camera 'load-test-camera-loadtest-agent-010-005' after 3 attempts
2025-07-23 17:48:38,184 - INFO - Processing camera 2/10: load-test-camera-loadtest-agent-010-004 (current: Off)
2025-07-23 17:48:38,185 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-004' recording to 'On' (attempt 1)
2025-07-23 17:48:38,232 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-004' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/eac0be98-81f0-44e9-801e-62303b7e513e
2025-07-23 17:48:39,233 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-004' recording to 'On' (attempt 2)
2025-07-23 17:48:39,278 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-004' (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/eac0be98-81f0-44e9-801e-62303b7e513e
2025-07-23 17:48:41,281 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-004' recording to 'On' (attempt 3)
2025-07-23 17:48:41,370 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-004' (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/eac0be98-81f0-44e9-801e-62303b7e513e
2025-07-23 17:48:41,370 - ERROR - Failed to update camera 'load-test-camera-loadtest-agent-010-004' after 3 attempts
2025-07-23 17:48:41,871 - INFO - Processing camera 3/10: load-test-camera-loadtest-agent-010-003 (current: Off)
2025-07-23 17:48:41,871 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-003' recording to 'On' (attempt 1)
2025-07-23 17:48:41,907 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-003' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/18b89e9b-293d-4c76-8db7-b10bdfb62018
2025-07-23 17:48:42,909 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-003' recording to 'On' (attempt 2)
2025-07-23 17:48:42,952 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-003' (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/18b89e9b-293d-4c76-8db7-b10bdfb62018
2025-07-23 17:48:44,954 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-003' recording to 'On' (attempt 3)
2025-07-23 17:48:44,994 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-003' (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/18b89e9b-293d-4c76-8db7-b10bdfb62018
2025-07-23 17:48:44,995 - ERROR - Failed to update camera 'load-test-camera-loadtest-agent-010-003' after 3 attempts
2025-07-23 17:48:45,496 - INFO - Processing camera 4/10: load-test-camera-loadtest-agent-010-002 (current: Off)
2025-07-23 17:48:45,497 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-002' recording to 'On' (attempt 1)
2025-07-23 17:48:45,540 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-002' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/8a770272-5b09-4e0b-8989-6e71cb73cb65
2025-07-23 17:48:46,542 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-002' recording to 'On' (attempt 2)
2025-07-23 17:48:46,580 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-002' (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/8a770272-5b09-4e0b-8989-6e71cb73cb65
2025-07-23 17:48:48,582 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-002' recording to 'On' (attempt 3)
2025-07-23 17:48:48,617 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-002' (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/8a770272-5b09-4e0b-8989-6e71cb73cb65
2025-07-23 17:48:48,617 - ERROR - Failed to update camera 'load-test-camera-loadtest-agent-010-002' after 3 attempts
2025-07-23 17:48:49,118 - INFO - Processing camera 5/10: load-test-camera-loadtest-agent-010-001 (current: Off)
2025-07-23 17:48:49,118 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-001' recording to 'On' (attempt 1)
2025-07-23 17:48:49,154 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-001' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/19ca344b-9238-449b-88e5-c2ebee601b0e
2025-07-23 17:48:50,155 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-001' recording to 'On' (attempt 2)
2025-07-23 17:48:50,194 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-001' (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/19ca344b-9238-449b-88e5-c2ebee601b0e
2025-07-23 17:48:52,197 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-001' recording to 'On' (attempt 3)
2025-07-23 17:48:52,233 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-001' (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/19ca344b-9238-449b-88e5-c2ebee601b0e
2025-07-23 17:48:52,234 - ERROR - Failed to update camera 'load-test-camera-loadtest-agent-010-001' after 3 attempts
2025-07-23 17:48:52,734 - INFO - Processing camera 6/10: load-test-camera-loadtest-agent-009-005 (current: Off)
2025-07-23 17:48:52,734 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-005' recording to 'On' (attempt 1)
2025-07-23 17:48:52,767 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-009-005' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/ffa6bd5c-ba7b-4e58-b329-d1429b58df3f
2025-07-23 17:48:53,768 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-005' recording to 'On' (attempt 2)
2025-07-23 17:48:53,799 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-009-005' (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/ffa6bd5c-ba7b-4e58-b329-d1429b58df3f
2025-07-23 17:48:55,802 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-005' recording to 'On' (attempt 3)
2025-07-23 17:48:55,849 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-009-005' (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/ffa6bd5c-ba7b-4e58-b329-d1429b58df3f
2025-07-23 17:48:55,849 - ERROR - Failed to update camera 'load-test-camera-loadtest-agent-009-005' after 3 attempts
2025-07-23 17:48:56,350 - INFO - Processing camera 7/10: load-test-camera-loadtest-agent-009-004 (current: Off)
2025-07-23 17:48:56,350 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-004' recording to 'On' (attempt 1)
2025-07-23 17:48:56,384 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-009-004' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/743296af-052a-4670-b5fe-e84168643949
2025-07-23 17:48:57,385 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-004' recording to 'On' (attempt 2)
2025-07-23 17:48:57,433 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-009-004' (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/743296af-052a-4670-b5fe-e84168643949
2025-07-23 17:48:59,435 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-004' recording to 'On' (attempt 3)
2025-07-23 17:48:59,466 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-009-004' (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/743296af-052a-4670-b5fe-e84168643949
2025-07-23 17:48:59,467 - ERROR - Failed to update camera 'load-test-camera-loadtest-agent-009-004' after 3 attempts
2025-07-23 17:48:59,968 - INFO - Processing camera 8/10: load-test-camera-loadtest-agent-009-003 (current: Off)
2025-07-23 17:48:59,968 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-003' recording to 'On' (attempt 1)
2025-07-23 17:49:00,003 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-009-003' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/9de51f5b-fec4-4e42-b25e-e012ec9e38f0
2025-07-23 17:49:01,005 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-003' recording to 'On' (attempt 2)
2025-07-23 17:49:01,044 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-009-003' (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/9de51f5b-fec4-4e42-b25e-e012ec9e38f0
2025-07-23 17:49:03,046 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-003' recording to 'On' (attempt 3)
2025-07-23 17:49:03,088 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-009-003' (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/9de51f5b-fec4-4e42-b25e-e012ec9e38f0
2025-07-23 17:49:03,088 - ERROR - Failed to update camera 'load-test-camera-loadtest-agent-009-003' after 3 attempts
2025-07-23 17:49:03,589 - INFO - Processing camera 9/10: load-test-camera-loadtest-agent-009-002 (current: Off)
2025-07-23 17:49:03,590 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-002' recording to 'On' (attempt 1)
2025-07-23 17:49:03,629 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-009-002' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/26866f7f-85be-4e53-85c9-776bb0785a35
2025-07-23 17:49:04,630 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-002' recording to 'On' (attempt 2)
2025-07-23 17:49:04,662 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-009-002' (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/26866f7f-85be-4e53-85c9-776bb0785a35
2025-07-23 17:49:06,664 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-002' recording to 'On' (attempt 3)
2025-07-23 17:49:06,704 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-009-002' (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/26866f7f-85be-4e53-85c9-776bb0785a35
2025-07-23 17:49:06,704 - ERROR - Failed to update camera 'load-test-camera-loadtest-agent-009-002' after 3 attempts
2025-07-23 17:49:07,205 - INFO - Processing camera 10/10: load-test-camera-loadtest-agent-009-001 (current: Off)
2025-07-23 17:49:07,205 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-001' recording to 'On' (attempt 1)
2025-07-23 17:49:07,236 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-009-001' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/8be14c3d-5832-462c-8bbb-2cc6a0c7ea6f
2025-07-23 17:49:08,237 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-001' recording to 'On' (attempt 2)
2025-07-23 17:49:08,270 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-009-001' (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/8be14c3d-5832-462c-8bbb-2cc6a0c7ea6f
2025-07-23 17:49:10,272 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-001' recording to 'On' (attempt 3)
2025-07-23 17:49:10,309 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-009-001' (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/8be14c3d-5832-462c-8bbb-2cc6a0c7ea6f
2025-07-23 17:49:10,309 - ERROR - Failed to update camera 'load-test-camera-loadtest-agent-009-001' after 3 attempts
2025-07-23 17:49:10,309 - INFO - Recording toggle complete: 0/10 cameras updated successfully
2025-07-23 17:49:10,310 - WARNING - Failed to update cameras: ['load-test-camera-loadtest-agent-010-005', 'load-test-camera-loadtest-agent-010-004', 'load-test-camera-loadtest-agent-010-003', 'load-test-camera-loadtest-agent-010-002', 'load-test-camera-loadtest-agent-010-001', 'load-test-camera-loadtest-agent-009-005', 'load-test-camera-loadtest-agent-009-004', 'load-test-camera-loadtest-agent-009-003', 'load-test-camera-loadtest-agent-009-002', 'load-test-camera-loadtest-agent-009-001']
2025-07-23 17:49:10,310 - ERROR - Recording toggle failed!
2025-07-23 17:59:21,640 - INFO - Starting recording toggle to 'On' for load test cameras
2025-07-23 17:59:21,640 - INFO - Attempting login (attempt 1)
2025-07-23 17:59:22,136 - INFO - Login successful! Token expires in 3600 seconds
2025-07-23 17:59:22,137 - INFO - Fetching all cameras (attempt 1)
2025-07-23 17:59:22,226 - INFO - Successfully fetched 10 cameras
2025-07-23 17:59:22,227 - INFO - Found 10 load test cameras out of 10 total cameras
2025-07-23 17:59:22,227 - INFO - Processing camera 1/10: load-test-camera-loadtest-agent-010-005 (current: Off)
2025-07-23 17:59:22,227 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-005' recording to 'On' (attempt 1)
2025-07-23 17:59:22,263 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-005' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/346e4d75-4a94-43b9-ad17-13fc7bf26f3d
2025-07-23 17:59:23,264 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-005' recording to 'On' (attempt 2)
2025-07-23 17:59:23,304 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-005' (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/346e4d75-4a94-43b9-ad17-13fc7bf26f3d
2025-07-23 17:59:25,306 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-005' recording to 'On' (attempt 3)
2025-07-23 17:59:25,348 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-005' (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/346e4d75-4a94-43b9-ad17-13fc7bf26f3d
2025-07-23 17:59:25,348 - ERROR - Failed to update camera 'load-test-camera-loadtest-agent-010-005' after 3 attempts
2025-07-23 17:59:25,849 - INFO - Processing camera 2/10: load-test-camera-loadtest-agent-010-004 (current: Off)
2025-07-23 17:59:25,849 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-004' recording to 'On' (attempt 1)
2025-07-23 17:59:25,885 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-004' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/eac0be98-81f0-44e9-801e-62303b7e513e
2025-07-23 17:59:26,886 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-004' recording to 'On' (attempt 2)
2025-07-23 17:59:26,924 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-004' (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/eac0be98-81f0-44e9-801e-62303b7e513e
2025-07-23 17:59:28,927 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-004' recording to 'On' (attempt 3)
2025-07-23 17:59:28,959 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-004' (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/eac0be98-81f0-44e9-801e-62303b7e513e
2025-07-23 17:59:28,959 - ERROR - Failed to update camera 'load-test-camera-loadtest-agent-010-004' after 3 attempts
2025-07-23 17:59:29,460 - INFO - Processing camera 3/10: load-test-camera-loadtest-agent-010-003 (current: Off)
2025-07-23 17:59:29,460 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-003' recording to 'On' (attempt 1)
2025-07-23 17:59:29,505 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-003' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/18b89e9b-293d-4c76-8db7-b10bdfb62018
2025-07-23 17:59:30,506 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-003' recording to 'On' (attempt 2)
2025-07-23 17:59:30,547 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-003' (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/18b89e9b-293d-4c76-8db7-b10bdfb62018
2025-07-23 17:59:32,549 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-003' recording to 'On' (attempt 3)
2025-07-23 17:59:32,589 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-003' (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/18b89e9b-293d-4c76-8db7-b10bdfb62018
2025-07-23 17:59:32,590 - ERROR - Failed to update camera 'load-test-camera-loadtest-agent-010-003' after 3 attempts
2025-07-23 17:59:33,091 - INFO - Processing camera 4/10: load-test-camera-loadtest-agent-010-002 (current: Off)
2025-07-23 17:59:33,091 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-002' recording to 'On' (attempt 1)
2025-07-23 17:59:33,133 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-002' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/8a770272-5b09-4e0b-8989-6e71cb73cb65
2025-07-23 17:59:34,135 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-002' recording to 'On' (attempt 2)
2025-07-23 17:59:34,176 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-002' (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/8a770272-5b09-4e0b-8989-6e71cb73cb65
2025-07-23 17:59:35,062 - INFO - Recording toggle interrupted by user
2025-07-23 18:00:07,040 - INFO - Starting recording toggle to 'On' for load test cameras
2025-07-23 18:00:07,040 - INFO - Attempting login (attempt 1)
2025-07-23 18:00:07,266 - INFO - Login successful! Token expires in 3600 seconds
2025-07-23 18:00:07,266 - INFO - Fetching all cameras (attempt 1)
2025-07-23 18:00:07,354 - INFO - Successfully fetched 10 cameras
2025-07-23 18:00:07,354 - INFO - Found 10 load test cameras out of 10 total cameras
2025-07-23 18:00:07,355 - INFO - Processing camera 1/10: load-test-camera-loadtest-agent-010-005 (current: Off)
2025-07-23 18:00:07,355 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-005' recording to 'On' (attempt 1)
2025-07-23 18:00:07,394 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-005' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/346e4d75-4a94-43b9-ad17-13fc7bf26f3d
2025-07-23 18:00:08,396 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-005' recording to 'On' (attempt 2)
2025-07-23 18:00:08,444 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-005' (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/346e4d75-4a94-43b9-ad17-13fc7bf26f3d
2025-07-23 18:00:10,446 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-005' recording to 'On' (attempt 3)
2025-07-23 18:00:10,486 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-005' (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/346e4d75-4a94-43b9-ad17-13fc7bf26f3d
2025-07-23 18:00:10,486 - ERROR - Failed to update camera 'load-test-camera-loadtest-agent-010-005' after 3 attempts
2025-07-23 18:00:10,987 - INFO - Processing camera 2/10: load-test-camera-loadtest-agent-010-004 (current: Off)
2025-07-23 18:00:10,987 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-004' recording to 'On' (attempt 1)
2025-07-23 18:00:11,019 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-004' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/eac0be98-81f0-44e9-801e-62303b7e513e
2025-07-23 18:00:11,758 - INFO - Recording toggle interrupted by user
2025-07-23 18:03:33,617 - INFO - Starting recording toggle to 'On' for load test cameras
2025-07-23 18:03:33,617 - INFO - Attempting login (attempt 1)
2025-07-23 18:03:34,059 - INFO - Login successful! Token expires in 3600 seconds
2025-07-23 18:03:34,059 - INFO - Fetching all cameras (attempt 1)
2025-07-23 18:03:34,136 - INFO - Successfully fetched 10 cameras
2025-07-23 18:03:34,136 - INFO - Found 10 load test cameras out of 10 total cameras
2025-07-23 18:03:34,136 - INFO - Processing camera 1/10: load-test-camera-loadtest-agent-010-005 (current: Off)
2025-07-23 18:03:34,136 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-005' recording to 'On' (attempt 1)
2025-07-23 18:03:34,172 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-005' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/346e4d75-4a94-43b9-ad17-13fc7bf26f3d
2025-07-23 18:03:35,174 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-005' recording to 'On' (attempt 2)
2025-07-23 18:03:35,208 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-005' (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/346e4d75-4a94-43b9-ad17-13fc7bf26f3d
2025-07-23 18:03:37,211 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-005' recording to 'On' (attempt 3)
2025-07-23 18:03:37,248 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-005' (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/346e4d75-4a94-43b9-ad17-13fc7bf26f3d
2025-07-23 18:03:37,248 - ERROR - Failed to update camera 'load-test-camera-loadtest-agent-010-005' after 3 attempts
2025-07-23 18:03:37,749 - INFO - Processing camera 2/10: load-test-camera-loadtest-agent-010-004 (current: Off)
2025-07-23 18:03:37,750 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-004' recording to 'On' (attempt 1)
2025-07-23 18:03:37,784 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-004' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/eac0be98-81f0-44e9-801e-62303b7e513e
2025-07-23 18:03:38,785 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-004' recording to 'On' (attempt 2)
2025-07-23 18:03:38,821 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-004' (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/eac0be98-81f0-44e9-801e-62303b7e513e
2025-07-23 18:03:40,824 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-004' recording to 'On' (attempt 3)
2025-07-23 18:03:40,861 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-004' (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/eac0be98-81f0-44e9-801e-62303b7e513e
2025-07-23 18:03:40,862 - ERROR - Failed to update camera 'load-test-camera-loadtest-agent-010-004' after 3 attempts
2025-07-23 18:03:41,363 - INFO - Processing camera 3/10: load-test-camera-loadtest-agent-010-003 (current: Off)
2025-07-23 18:03:41,363 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-003' recording to 'On' (attempt 1)
2025-07-23 18:03:41,401 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-003' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/18b89e9b-293d-4c76-8db7-b10bdfb62018
2025-07-23 18:03:42,402 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-003' recording to 'On' (attempt 2)
2025-07-23 18:03:42,439 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-003' (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/18b89e9b-293d-4c76-8db7-b10bdfb62018
2025-07-23 18:03:44,442 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-003' recording to 'On' (attempt 3)
2025-07-23 18:03:44,481 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-003' (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/18b89e9b-293d-4c76-8db7-b10bdfb62018
2025-07-23 18:03:44,481 - ERROR - Failed to update camera 'load-test-camera-loadtest-agent-010-003' after 3 attempts
2025-07-23 18:03:44,982 - INFO - Processing camera 4/10: load-test-camera-loadtest-agent-010-002 (current: Off)
2025-07-23 18:03:44,982 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-002' recording to 'On' (attempt 1)
2025-07-23 18:03:45,021 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-002' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/8a770272-5b09-4e0b-8989-6e71cb73cb65
2025-07-23 18:03:46,022 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-002' recording to 'On' (attempt 2)
2025-07-23 18:03:46,060 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-002' (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/8a770272-5b09-4e0b-8989-6e71cb73cb65
2025-07-23 18:03:48,063 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-002' recording to 'On' (attempt 3)
2025-07-23 18:03:48,098 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-002' (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/8a770272-5b09-4e0b-8989-6e71cb73cb65
2025-07-23 18:03:48,099 - ERROR - Failed to update camera 'load-test-camera-loadtest-agent-010-002' after 3 attempts
2025-07-23 18:03:48,599 - INFO - Processing camera 5/10: load-test-camera-loadtest-agent-010-001 (current: Off)
2025-07-23 18:03:48,600 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-001' recording to 'On' (attempt 1)
2025-07-23 18:03:48,635 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-001' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/19ca344b-9238-449b-88e5-c2ebee601b0e
2025-07-23 18:03:49,636 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-001' recording to 'On' (attempt 2)
2025-07-23 18:03:49,668 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-001' (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/19ca344b-9238-449b-88e5-c2ebee601b0e
2025-07-23 18:03:51,671 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-001' recording to 'On' (attempt 3)
2025-07-23 18:03:51,713 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-001' (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/19ca344b-9238-449b-88e5-c2ebee601b0e
2025-07-23 18:03:51,714 - ERROR - Failed to update camera 'load-test-camera-loadtest-agent-010-001' after 3 attempts
2025-07-23 18:03:52,214 - INFO - Processing camera 6/10: load-test-camera-loadtest-agent-009-005 (current: Off)
2025-07-23 18:03:52,214 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-005' recording to 'On' (attempt 1)
2025-07-23 18:03:52,245 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-009-005' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/ffa6bd5c-ba7b-4e58-b329-d1429b58df3f
2025-07-23 18:03:53,247 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-005' recording to 'On' (attempt 2)
2025-07-23 18:03:53,286 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-009-005' (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/ffa6bd5c-ba7b-4e58-b329-d1429b58df3f
2025-07-23 18:03:55,288 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-005' recording to 'On' (attempt 3)
2025-07-23 18:03:55,324 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-009-005' (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/ffa6bd5c-ba7b-4e58-b329-d1429b58df3f
2025-07-23 18:03:55,324 - ERROR - Failed to update camera 'load-test-camera-loadtest-agent-009-005' after 3 attempts
2025-07-23 18:03:55,825 - INFO - Processing camera 7/10: load-test-camera-loadtest-agent-009-004 (current: Off)
2025-07-23 18:03:55,825 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-004' recording to 'On' (attempt 1)
2025-07-23 18:03:55,861 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-009-004' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/743296af-052a-4670-b5fe-e84168643949
2025-07-23 18:03:56,862 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-004' recording to 'On' (attempt 2)
2025-07-23 18:03:56,897 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-009-004' (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/743296af-052a-4670-b5fe-e84168643949
2025-07-23 18:03:58,899 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-004' recording to 'On' (attempt 3)
2025-07-23 18:03:58,938 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-009-004' (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/743296af-052a-4670-b5fe-e84168643949
2025-07-23 18:03:58,938 - ERROR - Failed to update camera 'load-test-camera-loadtest-agent-009-004' after 3 attempts
2025-07-23 18:03:59,439 - INFO - Processing camera 8/10: load-test-camera-loadtest-agent-009-003 (current: Off)
2025-07-23 18:03:59,440 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-003' recording to 'On' (attempt 1)
2025-07-23 18:03:59,481 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-009-003' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/9de51f5b-fec4-4e42-b25e-e012ec9e38f0
2025-07-23 18:04:00,482 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-003' recording to 'On' (attempt 2)
2025-07-23 18:04:00,516 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-009-003' (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/9de51f5b-fec4-4e42-b25e-e012ec9e38f0
2025-07-23 18:04:02,517 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-003' recording to 'On' (attempt 3)
2025-07-23 18:04:02,564 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-009-003' (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/9de51f5b-fec4-4e42-b25e-e012ec9e38f0
2025-07-23 18:04:02,564 - ERROR - Failed to update camera 'load-test-camera-loadtest-agent-009-003' after 3 attempts
2025-07-23 18:04:03,065 - INFO - Processing camera 9/10: load-test-camera-loadtest-agent-009-002 (current: Off)
2025-07-23 18:04:03,065 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-002' recording to 'On' (attempt 1)
2025-07-23 18:04:03,108 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-009-002' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/26866f7f-85be-4e53-85c9-776bb0785a35
2025-07-23 18:04:04,109 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-002' recording to 'On' (attempt 2)
2025-07-23 18:04:04,147 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-009-002' (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/26866f7f-85be-4e53-85c9-776bb0785a35
2025-07-23 18:04:04,734 - INFO - Recording toggle interrupted by user
2025-07-24 08:46:37,136 - INFO - Starting recording toggle to 'On' for load test cameras
2025-07-24 08:46:37,136 - INFO - Attempting login (attempt 1)
2025-07-24 08:46:37,480 - INFO - Login successful! Token expires in 3600 seconds
2025-07-24 08:46:37,480 - INFO - Fetching all cameras (attempt 1)
2025-07-24 08:46:37,557 - INFO - Successfully fetched 10 cameras
2025-07-24 08:46:37,557 - INFO - Found 10 load test cameras out of 10 total cameras
2025-07-24 08:46:37,557 - INFO - Processing camera 1/10: load-test-camera-loadtest-agent-010-005 (current: Off)
2025-07-24 08:46:37,557 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-005' recording to 'On' (attempt 1)
2025-07-24 08:46:37,600 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-005' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/346e4d75-4a94-43b9-ad17-13fc7bf26f3d
2025-07-24 08:46:38,600 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-005' recording to 'On' (attempt 2)
2025-07-24 08:46:38,636 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-005' (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/346e4d75-4a94-43b9-ad17-13fc7bf26f3d
2025-07-24 08:46:40,637 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-005' recording to 'On' (attempt 3)
2025-07-24 08:46:40,672 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-005' (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/346e4d75-4a94-43b9-ad17-13fc7bf26f3d
2025-07-24 08:46:40,673 - ERROR - Failed to update camera 'load-test-camera-loadtest-agent-010-005' after 3 attempts
2025-07-24 08:46:41,173 - INFO - Processing camera 2/10: load-test-camera-loadtest-agent-010-004 (current: Off)
2025-07-24 08:46:41,174 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-004' recording to 'On' (attempt 1)
2025-07-24 08:46:41,210 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-004' (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/eac0be98-81f0-44e9-801e-62303b7e513e
2025-07-24 08:46:42,210 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-004' recording to 'On' (attempt 2)
2025-07-24 08:46:42,246 - WARNING - Failed to update camera 'load-test-camera-loadtest-agent-010-004' (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/eac0be98-81f0-44e9-801e-62303b7e513e
2025-07-24 08:46:42,380 - INFO - Recording toggle interrupted by user
2025-07-24 08:51:25,948 - INFO - Starting recording toggle to 'On' for load test cameras
2025-07-24 08:51:25,948 - INFO - Attempting login (attempt 1)
2025-07-24 08:51:26,117 - INFO - Login successful! Token expires in 3600 seconds
2025-07-24 08:51:26,117 - INFO - Fetching all cameras (attempt 1)
2025-07-24 08:51:26,176 - INFO - Successfully fetched 10 cameras
2025-07-24 08:51:26,176 - INFO - Found 10 load test cameras out of 10 total cameras
2025-07-24 08:51:26,176 - INFO - Processing camera 1/10: load-test-camera-loadtest-agent-010-005 (current: Off)
2025-07-24 08:51:26,176 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-005' recording to 'On' (attempt 1)
2025-07-24 08:51:26,231 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-005' recording to 'On'
2025-07-24 08:51:26,732 - INFO - Processing camera 2/10: load-test-camera-loadtest-agent-010-004 (current: Off)
2025-07-24 08:51:26,732 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-004' recording to 'On' (attempt 1)
2025-07-24 08:51:26,788 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-004' recording to 'On'
2025-07-24 08:51:27,289 - INFO - Processing camera 3/10: load-test-camera-loadtest-agent-010-003 (current: Off)
2025-07-24 08:51:27,289 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-003' recording to 'On' (attempt 1)
2025-07-24 08:51:27,343 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-003' recording to 'On'
2025-07-24 08:51:27,844 - INFO - Processing camera 4/10: load-test-camera-loadtest-agent-010-002 (current: Off)
2025-07-24 08:51:27,844 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-002' recording to 'On' (attempt 1)
2025-07-24 08:51:27,906 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-002' recording to 'On'
2025-07-24 08:51:28,407 - INFO - Processing camera 5/10: load-test-camera-loadtest-agent-010-001 (current: Off)
2025-07-24 08:51:28,407 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-001' recording to 'On' (attempt 1)
2025-07-24 08:51:28,460 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-001' recording to 'On'
2025-07-24 08:51:28,961 - INFO - Processing camera 6/10: load-test-camera-loadtest-agent-009-005 (current: Off)
2025-07-24 08:51:28,961 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-005' recording to 'On' (attempt 1)
2025-07-24 08:51:29,034 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-005' recording to 'On'
2025-07-24 08:51:29,534 - INFO - Processing camera 7/10: load-test-camera-loadtest-agent-009-004 (current: Off)
2025-07-24 08:51:29,534 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-004' recording to 'On' (attempt 1)
2025-07-24 08:51:29,591 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-004' recording to 'On'
2025-07-24 08:51:30,092 - INFO - Processing camera 8/10: load-test-camera-loadtest-agent-009-003 (current: Off)
2025-07-24 08:51:30,093 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-003' recording to 'On' (attempt 1)
2025-07-24 08:51:30,142 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-003' recording to 'On'
2025-07-24 08:51:30,642 - INFO - Processing camera 9/10: load-test-camera-loadtest-agent-009-002 (current: Off)
2025-07-24 08:51:30,643 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-002' recording to 'On' (attempt 1)
2025-07-24 08:51:30,702 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-002' recording to 'On'
2025-07-24 08:51:31,203 - INFO - Processing camera 10/10: load-test-camera-loadtest-agent-009-001 (current: Off)
2025-07-24 08:51:31,203 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-001' recording to 'On' (attempt 1)
2025-07-24 08:51:31,263 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-001' recording to 'On'
2025-07-24 08:51:31,263 - INFO - Recording toggle complete: 10/10 cameras updated successfully
2025-07-24 08:51:31,263 - INFO - All load test cameras recording set to 'On'!
2025-07-24 08:51:31,263 - INFO - Recording toggle completed successfully!
2025-07-24 09:21:02,462 - INFO - Starting recording toggle to 'On' for load test cameras
2025-07-24 09:21:02,463 - INFO - Attempting login (attempt 1)
2025-07-24 09:21:02,978 - INFO - Login successful! Token expires in 3600 seconds
2025-07-24 09:21:02,978 - INFO - Fetching all cameras (attempt 1)
2025-07-24 09:21:03,058 - INFO - Successfully fetched 10 cameras
2025-07-24 09:21:03,058 - INFO - Found 10 load test cameras out of 10 total cameras
2025-07-24 09:21:03,058 - INFO - Processing camera 1/10: load-test-camera-loadtest-agent-010-005 (current: On)
2025-07-24 09:21:03,058 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-005' recording to 'On' (attempt 1)
2025-07-24 09:21:03,107 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-005' recording to 'On'
2025-07-24 09:21:03,608 - INFO - Processing camera 2/10: load-test-camera-loadtest-agent-010-004 (current: On)
2025-07-24 09:21:03,608 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-004' recording to 'On' (attempt 1)
2025-07-24 09:21:03,662 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-004' recording to 'On'
2025-07-24 09:21:04,162 - INFO - Processing camera 3/10: load-test-camera-loadtest-agent-010-003 (current: On)
2025-07-24 09:21:04,162 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-003' recording to 'On' (attempt 1)
2025-07-24 09:21:04,221 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-003' recording to 'On'
2025-07-24 09:21:04,721 - INFO - Processing camera 4/10: load-test-camera-loadtest-agent-010-002 (current: On)
2025-07-24 09:21:04,721 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-002' recording to 'On' (attempt 1)
2025-07-24 09:21:04,796 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-002' recording to 'On'
2025-07-24 09:21:05,296 - INFO - Processing camera 5/10: load-test-camera-loadtest-agent-010-001 (current: On)
2025-07-24 09:21:05,296 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-001' recording to 'On' (attempt 1)
2025-07-24 09:21:05,413 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-001' recording to 'On'
2025-07-24 09:21:05,914 - INFO - Processing camera 6/10: load-test-camera-loadtest-agent-009-005 (current: On)
2025-07-24 09:21:05,914 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-005' recording to 'On' (attempt 1)
2025-07-24 09:21:05,973 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-005' recording to 'On'
2025-07-24 09:21:06,473 - INFO - Processing camera 7/10: load-test-camera-loadtest-agent-009-004 (current: On)
2025-07-24 09:21:06,473 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-004' recording to 'On' (attempt 1)
2025-07-24 09:21:06,539 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-004' recording to 'On'
2025-07-24 09:21:07,040 - INFO - Processing camera 8/10: load-test-camera-loadtest-agent-009-003 (current: On)
2025-07-24 09:21:07,040 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-003' recording to 'On' (attempt 1)
2025-07-24 09:21:07,096 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-003' recording to 'On'
2025-07-24 09:21:07,596 - INFO - Processing camera 9/10: load-test-camera-loadtest-agent-009-002 (current: On)
2025-07-24 09:21:07,596 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-002' recording to 'On' (attempt 1)
2025-07-24 09:21:07,650 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-002' recording to 'On'
2025-07-24 09:21:08,151 - INFO - Processing camera 10/10: load-test-camera-loadtest-agent-009-001 (current: On)
2025-07-24 09:21:08,151 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-001' recording to 'On' (attempt 1)
2025-07-24 09:21:08,201 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-001' recording to 'On'
2025-07-24 09:21:08,201 - INFO - Recording toggle complete: 10/10 cameras updated successfully
2025-07-24 09:21:08,201 - INFO - All load test cameras recording set to 'On'!
2025-07-24 09:21:08,201 - INFO - Recording toggle completed successfully!
2025-07-24 09:29:16,684 - INFO - Starting recording toggle to 'On' for load test cameras
2025-07-24 09:29:16,684 - INFO - Attempting login (attempt 1)
2025-07-24 09:29:16,894 - INFO - Login successful! Token expires in 3600 seconds
2025-07-24 09:29:16,894 - INFO - Fetching all cameras (attempt 1)
2025-07-24 09:29:16,959 - INFO - Successfully fetched 10 cameras
2025-07-24 09:29:16,959 - INFO - Found 10 load test cameras out of 10 total cameras
2025-07-24 09:29:16,959 - INFO - Processing camera 1/10: load-test-camera-loadtest-agent-010-005 (current: On)
2025-07-24 09:29:16,959 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-005' recording to 'On' (attempt 1)
2025-07-24 09:29:17,028 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-005' recording to 'On'
2025-07-24 09:29:17,529 - INFO - Processing camera 2/10: load-test-camera-loadtest-agent-010-004 (current: On)
2025-07-24 09:29:17,529 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-004' recording to 'On' (attempt 1)
2025-07-24 09:29:17,586 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-004' recording to 'On'
2025-07-24 09:29:18,087 - INFO - Processing camera 3/10: load-test-camera-loadtest-agent-010-003 (current: On)
2025-07-24 09:29:18,087 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-003' recording to 'On' (attempt 1)
2025-07-24 09:29:18,168 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-003' recording to 'On'
2025-07-24 09:29:18,668 - INFO - Processing camera 4/10: load-test-camera-loadtest-agent-010-002 (current: On)
2025-07-24 09:29:18,668 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-002' recording to 'On' (attempt 1)
2025-07-24 09:29:18,904 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-002' recording to 'On'
2025-07-24 09:29:19,404 - INFO - Processing camera 5/10: load-test-camera-loadtest-agent-010-001 (current: On)
2025-07-24 09:29:19,405 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-001' recording to 'On' (attempt 1)
2025-07-24 09:29:19,465 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-001' recording to 'On'
2025-07-24 09:29:19,965 - INFO - Processing camera 6/10: load-test-camera-loadtest-agent-009-005 (current: On)
2025-07-24 09:29:19,965 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-005' recording to 'On' (attempt 1)
2025-07-24 09:29:20,021 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-005' recording to 'On'
2025-07-24 09:29:20,522 - INFO - Processing camera 7/10: load-test-camera-loadtest-agent-009-004 (current: On)
2025-07-24 09:29:20,522 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-004' recording to 'On' (attempt 1)
2025-07-24 09:29:20,574 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-004' recording to 'On'
2025-07-24 09:29:21,075 - INFO - Processing camera 8/10: load-test-camera-loadtest-agent-009-003 (current: On)
2025-07-24 09:29:21,075 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-003' recording to 'On' (attempt 1)
2025-07-24 09:29:21,126 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-003' recording to 'On'
2025-07-24 09:29:21,627 - INFO - Processing camera 9/10: load-test-camera-loadtest-agent-009-002 (current: On)
2025-07-24 09:29:21,627 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-002' recording to 'On' (attempt 1)
2025-07-24 09:29:21,683 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-002' recording to 'On'
2025-07-24 09:29:22,183 - INFO - Processing camera 10/10: load-test-camera-loadtest-agent-009-001 (current: On)
2025-07-24 09:29:22,183 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-001' recording to 'On' (attempt 1)
2025-07-24 09:29:22,238 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-001' recording to 'On'
2025-07-24 09:29:22,238 - INFO - Recording toggle complete: 10/10 cameras updated successfully
2025-07-24 09:29:22,238 - INFO - All load test cameras recording set to 'On'!
2025-07-24 09:29:22,238 - INFO - Recording toggle completed successfully!
2025-07-24 09:33:45,167 - INFO - Starting recording toggle to 'On' for load test cameras
2025-07-24 09:33:45,168 - INFO - Attempting login (attempt 1)
2025-07-24 09:33:45,359 - INFO - Login successful! Token expires in 3600 seconds
2025-07-24 09:33:45,360 - INFO - Fetching all cameras (attempt 1)
2025-07-24 09:33:45,435 - INFO - Successfully fetched 10 cameras
2025-07-24 09:33:45,435 - INFO - Found 10 load test cameras out of 10 total cameras
2025-07-24 09:33:45,435 - INFO - Processing camera 1/10: load-test-camera-loadtest-agent-010-005 (current: Off)
2025-07-24 09:33:45,435 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-005' recording to 'On' (attempt 1)
2025-07-24 09:33:45,492 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-005' recording to 'On'
2025-07-24 09:33:45,992 - INFO - Processing camera 2/10: load-test-camera-loadtest-agent-010-004 (current: Off)
2025-07-24 09:33:45,992 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-004' recording to 'On' (attempt 1)
2025-07-24 09:33:46,052 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-004' recording to 'On'
2025-07-24 09:33:46,552 - INFO - Processing camera 3/10: load-test-camera-loadtest-agent-010-003 (current: Off)
2025-07-24 09:33:46,552 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-003' recording to 'On' (attempt 1)
2025-07-24 09:33:46,614 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-003' recording to 'On'
2025-07-24 09:33:47,114 - INFO - Processing camera 4/10: load-test-camera-loadtest-agent-010-002 (current: Off)
2025-07-24 09:33:47,114 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-002' recording to 'On' (attempt 1)
2025-07-24 09:33:47,179 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-002' recording to 'On'
2025-07-24 09:33:47,680 - INFO - Processing camera 5/10: load-test-camera-loadtest-agent-010-001 (current: Off)
2025-07-24 09:33:47,680 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-001' recording to 'On' (attempt 1)
2025-07-24 09:33:47,739 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-001' recording to 'On'
2025-07-24 09:33:48,239 - INFO - Processing camera 6/10: load-test-camera-loadtest-agent-009-005 (current: Off)
2025-07-24 09:33:48,240 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-005' recording to 'On' (attempt 1)
2025-07-24 09:33:48,295 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-005' recording to 'On'
2025-07-24 09:33:48,795 - INFO - Processing camera 7/10: load-test-camera-loadtest-agent-009-004 (current: Off)
2025-07-24 09:33:48,795 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-004' recording to 'On' (attempt 1)
2025-07-24 09:33:48,854 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-004' recording to 'On'
2025-07-24 09:33:49,355 - INFO - Processing camera 8/10: load-test-camera-loadtest-agent-009-003 (current: Off)
2025-07-24 09:33:49,355 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-003' recording to 'On' (attempt 1)
2025-07-24 09:33:49,413 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-003' recording to 'On'
2025-07-24 09:33:49,914 - INFO - Processing camera 9/10: load-test-camera-loadtest-agent-009-002 (current: Off)
2025-07-24 09:33:49,914 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-002' recording to 'On' (attempt 1)
2025-07-24 09:33:49,971 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-002' recording to 'On'
2025-07-24 09:33:50,471 - INFO - Processing camera 10/10: load-test-camera-loadtest-agent-009-001 (current: Off)
2025-07-24 09:33:50,471 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-001' recording to 'On' (attempt 1)
2025-07-24 09:33:50,530 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-001' recording to 'On'
2025-07-24 09:33:50,530 - INFO - Recording toggle complete: 10/10 cameras updated successfully
2025-07-24 09:33:50,530 - INFO - All load test cameras recording set to 'On'!
2025-07-24 09:33:50,530 - INFO - Recording toggle completed successfully!
2025-07-24 10:16:05,091 - INFO - Starting recording toggle to 'Off' for load test cameras
2025-07-24 10:16:05,091 - INFO - Attempting login (attempt 1)
2025-07-24 10:16:05,658 - INFO - Login successful! Token expires in 3600 seconds
2025-07-24 10:16:05,658 - INFO - Fetching all cameras (attempt 1)
2025-07-24 10:16:05,737 - INFO - Successfully fetched 10 cameras
2025-07-24 10:16:05,737 - INFO - Found 10 load test cameras out of 10 total cameras
2025-07-24 10:16:05,737 - INFO - Processing camera 1/10: load-test-camera-loadtest-agent-010-005 (current: On)
2025-07-24 10:16:05,737 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-005' recording to 'Off' (attempt 1)
2025-07-24 10:16:05,802 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-005' recording to 'Off'
2025-07-24 10:16:06,302 - INFO - Processing camera 2/10: load-test-camera-loadtest-agent-010-004 (current: On)
2025-07-24 10:16:06,302 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-004' recording to 'Off' (attempt 1)
2025-07-24 10:16:06,356 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-004' recording to 'Off'
2025-07-24 10:16:06,856 - INFO - Processing camera 3/10: load-test-camera-loadtest-agent-010-003 (current: On)
2025-07-24 10:16:06,856 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-003' recording to 'Off' (attempt 1)
2025-07-24 10:16:06,918 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-003' recording to 'Off'
2025-07-24 10:16:07,418 - INFO - Processing camera 4/10: load-test-camera-loadtest-agent-010-002 (current: On)
2025-07-24 10:16:07,418 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-002' recording to 'Off' (attempt 1)
2025-07-24 10:16:07,477 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-002' recording to 'Off'
2025-07-24 10:16:07,977 - INFO - Processing camera 5/10: load-test-camera-loadtest-agent-010-001 (current: On)
2025-07-24 10:16:07,978 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-001' recording to 'Off' (attempt 1)
2025-07-24 10:16:08,034 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-001' recording to 'Off'
2025-07-24 10:16:08,534 - INFO - Processing camera 6/10: load-test-camera-loadtest-agent-009-005 (current: On)
2025-07-24 10:16:08,534 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-005' recording to 'Off' (attempt 1)
2025-07-24 10:16:08,638 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-005' recording to 'Off'
2025-07-24 10:16:09,139 - INFO - Processing camera 7/10: load-test-camera-loadtest-agent-009-004 (current: On)
2025-07-24 10:16:09,139 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-004' recording to 'Off' (attempt 1)
2025-07-24 10:16:09,209 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-004' recording to 'Off'
2025-07-24 10:16:09,709 - INFO - Processing camera 8/10: load-test-camera-loadtest-agent-009-003 (current: On)
2025-07-24 10:16:09,709 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-003' recording to 'Off' (attempt 1)
2025-07-24 10:16:09,760 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-003' recording to 'Off'
2025-07-24 10:16:10,261 - INFO - Processing camera 9/10: load-test-camera-loadtest-agent-009-002 (current: On)
2025-07-24 10:16:10,261 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-002' recording to 'Off' (attempt 1)
2025-07-24 10:16:10,324 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-002' recording to 'Off'
2025-07-24 10:16:10,825 - INFO - Processing camera 10/10: load-test-camera-loadtest-agent-009-001 (current: On)
2025-07-24 10:16:10,825 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-001' recording to 'Off' (attempt 1)
2025-07-24 10:16:10,883 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-001' recording to 'Off'
2025-07-24 10:16:10,883 - INFO - Recording toggle complete: 10/10 cameras updated successfully
2025-07-24 10:16:10,883 - INFO - All load test cameras recording set to 'Off'!
2025-07-24 10:16:10,883 - INFO - Recording toggle completed successfully!
2025-07-24 10:32:47,191 - INFO - Starting recording toggle to 'On' for load test cameras
2025-07-24 10:32:47,191 - INFO - Attempting login (attempt 1)
2025-07-24 10:32:47,533 - INFO - Login successful! Token expires in 3600 seconds
2025-07-24 10:32:47,533 - INFO - Fetching all cameras (attempt 1)
2025-07-24 10:32:47,610 - INFO - Successfully fetched 10 cameras
2025-07-24 10:32:47,610 - INFO - Found 10 load test cameras out of 10 total cameras
2025-07-24 10:32:47,610 - INFO - Processing camera 1/10: load-test-camera-loadtest-agent-010-005 (current: Off)
2025-07-24 10:32:47,610 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-005' recording to 'On' (attempt 1)
2025-07-24 10:32:47,670 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-005' recording to 'On'
2025-07-24 10:32:48,171 - INFO - Processing camera 2/10: load-test-camera-loadtest-agent-010-004 (current: Off)
2025-07-24 10:32:48,171 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-004' recording to 'On' (attempt 1)
2025-07-24 10:32:48,227 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-004' recording to 'On'
2025-07-24 10:32:48,728 - INFO - Processing camera 3/10: load-test-camera-loadtest-agent-010-003 (current: Off)
2025-07-24 10:32:48,728 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-003' recording to 'On' (attempt 1)
2025-07-24 10:32:48,781 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-003' recording to 'On'
2025-07-24 10:32:49,281 - INFO - Processing camera 4/10: load-test-camera-loadtest-agent-010-002 (current: Off)
2025-07-24 10:32:49,281 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-002' recording to 'On' (attempt 1)
2025-07-24 10:32:49,535 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-002' recording to 'On'
2025-07-24 10:32:50,035 - INFO - Processing camera 5/10: load-test-camera-loadtest-agent-010-001 (current: Off)
2025-07-24 10:32:50,036 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-001' recording to 'On' (attempt 1)
2025-07-24 10:32:50,089 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-001' recording to 'On'
2025-07-24 10:32:50,589 - INFO - Processing camera 6/10: load-test-camera-loadtest-agent-009-005 (current: Off)
2025-07-24 10:32:50,589 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-005' recording to 'On' (attempt 1)
2025-07-24 10:32:50,645 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-005' recording to 'On'
2025-07-24 10:32:51,146 - INFO - Processing camera 7/10: load-test-camera-loadtest-agent-009-004 (current: Off)
2025-07-24 10:32:51,146 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-004' recording to 'On' (attempt 1)
2025-07-24 10:32:51,206 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-004' recording to 'On'
2025-07-24 10:32:51,707 - INFO - Processing camera 8/10: load-test-camera-loadtest-agent-009-003 (current: Off)
2025-07-24 10:32:51,707 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-003' recording to 'On' (attempt 1)
2025-07-24 10:32:51,757 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-003' recording to 'On'
2025-07-24 10:32:52,258 - INFO - Processing camera 9/10: load-test-camera-loadtest-agent-009-002 (current: Off)
2025-07-24 10:32:52,258 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-002' recording to 'On' (attempt 1)
2025-07-24 10:32:52,317 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-002' recording to 'On'
2025-07-24 10:32:52,818 - INFO - Processing camera 10/10: load-test-camera-loadtest-agent-009-001 (current: Off)
2025-07-24 10:32:52,818 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-001' recording to 'On' (attempt 1)
2025-07-24 10:32:52,884 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-001' recording to 'On'
2025-07-24 10:32:52,884 - INFO - Recording toggle complete: 10/10 cameras updated successfully
2025-07-24 10:32:52,884 - INFO - All load test cameras recording set to 'On'!
2025-07-24 10:32:52,884 - INFO - Recording toggle completed successfully!
2025-07-24 15:32:23,138 - INFO - Starting recording toggle to 'On' for load test cameras
2025-07-24 15:32:23,138 - INFO - Attempting login (attempt 1)
2025-07-24 15:32:23,355 - INFO - Login successful! Token expires in 3600 seconds
2025-07-24 15:32:23,355 - INFO - Fetching all cameras (attempt 1)
2025-07-24 15:32:23,429 - INFO - Successfully fetched 10 cameras
2025-07-24 15:32:23,429 - INFO - Found 10 load test cameras out of 10 total cameras
2025-07-24 15:32:23,429 - INFO - Processing camera 1/10: load-test-camera-loadtest-agent-010-005 (current: Off)
2025-07-24 15:32:23,429 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-005' recording to 'On' (attempt 1)
2025-07-24 15:32:23,485 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-005' recording to 'On'
2025-07-24 15:32:23,986 - INFO - Processing camera 2/10: load-test-camera-loadtest-agent-010-004 (current: Off)
2025-07-24 15:32:23,986 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-004' recording to 'On' (attempt 1)
2025-07-24 15:32:24,049 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-004' recording to 'On'
2025-07-24 15:32:24,550 - INFO - Processing camera 3/10: load-test-camera-loadtest-agent-010-003 (current: Off)
2025-07-24 15:32:24,550 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-003' recording to 'On' (attempt 1)
2025-07-24 15:32:24,605 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-003' recording to 'On'
2025-07-24 15:32:25,106 - INFO - Processing camera 4/10: load-test-camera-loadtest-agent-010-002 (current: Off)
2025-07-24 15:32:25,106 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-002' recording to 'On' (attempt 1)
2025-07-24 15:32:25,166 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-002' recording to 'On'
2025-07-24 15:32:25,666 - INFO - Processing camera 5/10: load-test-camera-loadtest-agent-010-001 (current: Off)
2025-07-24 15:32:25,667 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-001' recording to 'On' (attempt 1)
2025-07-24 15:32:25,720 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-001' recording to 'On'
2025-07-24 15:32:26,221 - INFO - Processing camera 6/10: load-test-camera-loadtest-agent-009-005 (current: Off)
2025-07-24 15:32:26,221 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-005' recording to 'On' (attempt 1)
2025-07-24 15:32:26,284 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-005' recording to 'On'
2025-07-24 15:32:26,784 - INFO - Processing camera 7/10: load-test-camera-loadtest-agent-009-004 (current: Off)
2025-07-24 15:32:26,785 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-004' recording to 'On' (attempt 1)
2025-07-24 15:32:26,837 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-004' recording to 'On'
2025-07-24 15:32:27,338 - INFO - Processing camera 8/10: load-test-camera-loadtest-agent-009-003 (current: Off)
2025-07-24 15:32:27,338 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-003' recording to 'On' (attempt 1)
2025-07-24 15:32:27,400 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-003' recording to 'On'
2025-07-24 15:32:27,900 - INFO - Processing camera 9/10: load-test-camera-loadtest-agent-009-002 (current: Off)
2025-07-24 15:32:27,900 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-002' recording to 'On' (attempt 1)
2025-07-24 15:32:27,959 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-002' recording to 'On'
2025-07-24 15:32:28,459 - INFO - Processing camera 10/10: load-test-camera-loadtest-agent-009-001 (current: Off)
2025-07-24 15:32:28,459 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-001' recording to 'On' (attempt 1)
2025-07-24 15:32:28,516 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-001' recording to 'On'
2025-07-24 15:32:28,516 - INFO - Recording toggle complete: 10/10 cameras updated successfully
2025-07-24 15:32:28,516 - INFO - All load test cameras recording set to 'On'!
2025-07-24 15:32:28,516 - INFO - Recording toggle completed successfully!
2025-07-25 12:22:52,753 - INFO - Starting recording toggle to 'On' for load test cameras
2025-07-25 12:22:52,754 - INFO - Attempting login (attempt 1)
2025-07-25 12:22:53,032 - INFO - Login successful! Token expires in 3600 seconds
2025-07-25 12:22:53,032 - INFO - Fetching all cameras (attempt 1)
2025-07-25 12:22:53,114 - INFO - Successfully fetched 10 cameras
2025-07-25 12:22:53,115 - INFO - Found 10 load test cameras out of 10 total cameras
2025-07-25 12:22:53,115 - INFO - Processing camera 1/10: load-test-camera-loadtest-agent-010-005 (current: On)
2025-07-25 12:22:53,115 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-005' recording to 'On' (attempt 1)
2025-07-25 12:22:53,181 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-005' recording to 'On'
2025-07-25 12:22:53,682 - INFO - Processing camera 2/10: load-test-camera-loadtest-agent-010-004 (current: On)
2025-07-25 12:22:53,682 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-004' recording to 'On' (attempt 1)
2025-07-25 12:22:53,742 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-004' recording to 'On'
2025-07-25 12:22:54,243 - INFO - Processing camera 3/10: load-test-camera-loadtest-agent-010-003 (current: On)
2025-07-25 12:22:54,244 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-003' recording to 'On' (attempt 1)
2025-07-25 12:22:54,312 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-003' recording to 'On'
2025-07-25 12:22:54,812 - INFO - Processing camera 4/10: load-test-camera-loadtest-agent-010-002 (current: On)
2025-07-25 12:22:54,813 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-002' recording to 'On' (attempt 1)
2025-07-25 12:22:54,883 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-002' recording to 'On'
2025-07-25 12:22:55,383 - INFO - Processing camera 5/10: load-test-camera-loadtest-agent-010-001 (current: On)
2025-07-25 12:22:55,384 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-001' recording to 'On' (attempt 1)
2025-07-25 12:22:55,449 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-001' recording to 'On'
2025-07-25 12:22:55,950 - INFO - Processing camera 6/10: load-test-camera-loadtest-agent-009-005 (current: On)
2025-07-25 12:22:55,950 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-005' recording to 'On' (attempt 1)
2025-07-25 12:22:56,010 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-005' recording to 'On'
2025-07-25 12:22:56,511 - INFO - Processing camera 7/10: load-test-camera-loadtest-agent-009-004 (current: On)
2025-07-25 12:22:56,511 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-004' recording to 'On' (attempt 1)
2025-07-25 12:22:56,580 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-004' recording to 'On'
2025-07-25 12:22:57,081 - INFO - Processing camera 8/10: load-test-camera-loadtest-agent-009-003 (current: On)
2025-07-25 12:22:57,082 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-003' recording to 'On' (attempt 1)
2025-07-25 12:22:57,157 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-003' recording to 'On'
2025-07-25 12:22:57,657 - INFO - Processing camera 9/10: load-test-camera-loadtest-agent-009-002 (current: On)
2025-07-25 12:22:57,658 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-002' recording to 'On' (attempt 1)
2025-07-25 12:22:57,723 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-002' recording to 'On'
2025-07-25 12:22:58,224 - INFO - Processing camera 10/10: load-test-camera-loadtest-agent-009-001 (current: On)
2025-07-25 12:22:58,224 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-001' recording to 'On' (attempt 1)
2025-07-25 12:22:58,288 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-001' recording to 'On'
2025-07-25 12:22:58,289 - INFO - Recording toggle complete: 10/10 cameras updated successfully
2025-07-25 12:22:58,290 - INFO - All load test cameras recording set to 'On'!
2025-07-25 12:22:58,290 - INFO - Recording toggle completed successfully!
2025-07-25 12:23:53,984 - INFO - Starting recording toggle to 'On' for load test cameras
2025-07-25 12:23:53,984 - INFO - Attempting login (attempt 1)
2025-07-25 12:23:54,283 - INFO - Login successful! Token expires in 3600 seconds
2025-07-25 12:23:54,283 - INFO - Fetching all cameras (attempt 1)
2025-07-25 12:23:54,372 - INFO - Successfully fetched 10 cameras
2025-07-25 12:23:54,372 - INFO - Found 10 load test cameras out of 10 total cameras
2025-07-25 12:23:54,372 - INFO - Processing camera 1/10: load-test-camera-loadtest-agent-010-005 (current: On)
2025-07-25 12:23:54,373 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-005' recording to 'On' (attempt 1)
2025-07-25 12:23:54,428 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-005' recording to 'On'
2025-07-25 12:23:54,931 - INFO - Processing camera 2/10: load-test-camera-loadtest-agent-010-004 (current: On)
2025-07-25 12:23:54,932 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-004' recording to 'On' (attempt 1)
2025-07-25 12:23:54,992 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-004' recording to 'On'
2025-07-25 12:23:55,493 - INFO - Processing camera 3/10: load-test-camera-loadtest-agent-010-003 (current: On)
2025-07-25 12:23:55,494 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-003' recording to 'On' (attempt 1)
2025-07-25 12:23:55,568 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-003' recording to 'On'
2025-07-25 12:23:56,069 - INFO - Processing camera 4/10: load-test-camera-loadtest-agent-010-002 (current: On)
2025-07-25 12:23:56,069 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-002' recording to 'On' (attempt 1)
2025-07-25 12:23:56,137 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-002' recording to 'On'
2025-07-25 12:23:56,638 - INFO - Processing camera 5/10: load-test-camera-loadtest-agent-010-001 (current: On)
2025-07-25 12:23:56,639 - INFO - Updating camera 'load-test-camera-loadtest-agent-010-001' recording to 'On' (attempt 1)
2025-07-25 12:23:56,707 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-010-001' recording to 'On'
2025-07-25 12:23:57,208 - INFO - Processing camera 6/10: load-test-camera-loadtest-agent-009-005 (current: On)
2025-07-25 12:23:57,209 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-005' recording to 'On' (attempt 1)
2025-07-25 12:23:57,277 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-005' recording to 'On'
2025-07-25 12:23:57,778 - INFO - Processing camera 7/10: load-test-camera-loadtest-agent-009-004 (current: On)
2025-07-25 12:23:57,779 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-004' recording to 'On' (attempt 1)
2025-07-25 12:23:57,838 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-004' recording to 'On'
2025-07-25 12:23:58,340 - INFO - Processing camera 8/10: load-test-camera-loadtest-agent-009-003 (current: On)
2025-07-25 12:23:58,341 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-003' recording to 'On' (attempt 1)
2025-07-25 12:23:58,401 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-003' recording to 'On'
2025-07-25 12:23:58,902 - INFO - Processing camera 9/10: load-test-camera-loadtest-agent-009-002 (current: On)
2025-07-25 12:23:58,903 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-002' recording to 'On' (attempt 1)
2025-07-25 12:23:58,969 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-002' recording to 'On'
2025-07-25 12:23:59,471 - INFO - Processing camera 10/10: load-test-camera-loadtest-agent-009-001 (current: On)
2025-07-25 12:23:59,471 - INFO - Updating camera 'load-test-camera-loadtest-agent-009-001' recording to 'On' (attempt 1)
2025-07-25 12:23:59,529 - INFO - Successfully updated camera 'load-test-camera-loadtest-agent-009-001' recording to 'On'
2025-07-25 12:23:59,530 - INFO - Recording toggle complete: 10/10 cameras updated successfully
2025-07-25 12:23:59,530 - INFO - All load test cameras recording set to 'On'!
2025-07-25 12:23:59,530 - INFO - Recording toggle completed successfully!
