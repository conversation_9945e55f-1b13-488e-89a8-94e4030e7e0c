#!/usr/bin/env python3
"""
Add a VA (Video Analytics) plugin to each loadtest camera
The VA to add is: aa2e35c5-1762-4666-854b-151dc50517ee

Flow:
1. <PERSON>gin to get bearer token
2. Get all cameras from API
3. Filter to load test cameras (matching naming pattern)
4. Update each camera to add the VA plugin
"""

"""
Endpoint to use is PUT /cameras/<camera_id>

Example payload to PUT /cameras/{id}
{
    "agent_id": "89dd4c87-f1ba-46d5-92ec-b8b291e7bee4",
    "name": "load-test-camera-loadtest-agent-010-005",
    "project_id": "c27e395c-09a7-433d-a6bc-c3262604cc87",
    "notes": "",
    "rtsp": "rtsp://172.16.0.9:8554/stream5",
    "va_plugin_ids": [
        "aa2e35c5-1762-4666-854b-151dc50517ee"
    ]
}
"""

import requests
import time
import logging
from typing import List, Dict, Optional

# Configuration (should match load-test.py)
API_BASE_URL = "https://api.volks-stg.knizsoft.com/api/v1"
# API_BASE_URL = "https://api.volks-dev.knizsoft.com/api/v1"
API_CAMERA = "/cameras"

# Login Info
LOGIN_ENDPOINT = "https://auth.volks-stg.knizsoft.com/token?grant_type=password"
# LOGIN_ENDPOINT = "https://auth.volks-dev.knizsoft.com/token?grant_type=password"
ADMIN_EMAIL = "<EMAIL>"
ADMIN_PASSWORD = "123456aA@"

# VA Plugin Configuration
VA_PLUGIN_ID = "aa2e35c5-1762-4666-854b-151dc50517ee"  # The VA plugin to add to cameras

# Camera identification
CAMERA_NAME_PREFIX = "load-test-camera-"  # Cameras created by load test start with this

REQUEST_TIMEOUT = 30
RETRY_ATTEMPTS = 3
DELAY_BETWEEN_REQUESTS = 0.5  # seconds

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('add_va.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class VAPluginManager:
    """Handles adding VA plugins to load test cameras"""

    def __init__(self, api_base_url: str = API_BASE_URL):
        self.api_base_url = api_base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'VAPluginManager/1.0'
        })
        self.bearer_token = None

    def login(self) -> bool:
        """
        Login to get bearer token for authentication

        Returns:
            True if login successful, False otherwise
        """
        payload = {
            "email": ADMIN_EMAIL,
            "password": ADMIN_PASSWORD
        }

        for attempt in range(RETRY_ATTEMPTS):
            try:
                logger.info(f"Attempting login (attempt {attempt + 1})")
                response = self.session.post(LOGIN_ENDPOINT, json=payload, timeout=REQUEST_TIMEOUT)
                response.raise_for_status()

                login_data = response.json()

                # Extract access token
                access_token = login_data.get('access_token')
                if not access_token:
                    logger.error("No access token returned from login")
                    return False

                self.bearer_token = access_token

                # Update session headers with bearer token
                self.session.headers.update({
                    'Authorization': f'Bearer {self.bearer_token}'
                })

                # Log token expiry info
                expires_in = login_data.get('expires_in', 'unknown')
                logger.info(f"Login successful! Token expires in {expires_in} seconds")
                return True

            except requests.exceptions.RequestException as e:
                logger.warning(f"Login failed (attempt {attempt + 1}): {e}")
                if attempt < RETRY_ATTEMPTS - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff

        logger.error(f"Login failed after {RETRY_ATTEMPTS} attempts")
        return False

    def get_all_cameras(self) -> List[Dict]:
        """
        Get all cameras from the API

        Returns:
            List of camera dictionaries, or empty list if failed
        """
        url = f"{self.api_base_url}{API_CAMERA}"

        for attempt in range(RETRY_ATTEMPTS):
            try:
                logger.info(f"Fetching all cameras (attempt {attempt + 1})")
                response = self.session.get(url, timeout=REQUEST_TIMEOUT)
                response.raise_for_status()

                response_data = response.json()

                # Check if API returned success
                if response_data.get('code') != 0:
                    logger.error(f"API returned error: {response_data.get('message', 'Unknown error')}")
                    return []

                cameras_data = response_data.get('data', [])
                logger.info(f"Successfully fetched {len(cameras_data)} cameras")
                return cameras_data

            except requests.exceptions.RequestException as e:
                logger.warning(f"Failed to fetch cameras (attempt {attempt + 1}): {e}")
                if attempt < RETRY_ATTEMPTS - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff

        logger.error(f"Failed to fetch cameras after {RETRY_ATTEMPTS} attempts")
        return []

    def filter_load_test_cameras(self, cameras: List[Dict]) -> List[Dict]:
        """
        Filter cameras to only include those created by load test

        Args:
            cameras: List of all cameras

        Returns:
            List of load test cameras
        """
        load_test_cameras = []
        for camera in cameras:
            camera_name = camera.get('name', '')
            if camera_name.startswith(CAMERA_NAME_PREFIX):
                load_test_cameras.append(camera)

        logger.info(f"Found {len(load_test_cameras)} load test cameras out of {len(cameras)} total cameras")
        return load_test_cameras

    def add_va_plugin_to_camera(self, camera: Dict) -> bool:
        """
        Add VA plugin to a specific camera

        Args:
            camera: Camera dictionary from API

        Returns:
            True if successful, False otherwise
        """
        camera_id = camera.get('id')
        camera_name = camera.get('name', 'Unknown')

        if not camera_id:
            logger.error(f"No camera ID found for camera '{camera_name}'")
            return False

        url = f"{self.api_base_url}{API_CAMERA}/{camera_id}"

        # Get current VA plugin IDs and add the new one if not already present
        current_va_plugins = camera.get('va_plugins', [])
        current_va_plugin_ids = [plugin.get('id') for plugin in current_va_plugins if plugin.get('id')]

        # Check if VA plugin is already added
        if VA_PLUGIN_ID in current_va_plugin_ids:
            logger.info(f"VA plugin {VA_PLUGIN_ID} already exists on camera '{camera_name}', skipping")
            return True

        # Add the new VA plugin ID to existing ones
        updated_va_plugin_ids = current_va_plugin_ids + [VA_PLUGIN_ID]

        # Prepare payload with all required fields
        payload = {
            "name": camera_name,
            "rtsp": camera.get('rtsp', ''),
            "tags": camera.get('tags', []),
            "notes": camera.get('notes', ''),
            "recording": camera.get('recording', 'Off'),
            "agent_id": camera.get('agent', {}).get('id') if camera.get('agent') else None,
            "project_id": camera.get('project', {}).get('id') if camera.get('project') else None,
            "va_plugin_ids": updated_va_plugin_ids
        }

        # Validate required fields
        if not payload["agent_id"]:
            logger.error(f"No agent ID found for camera '{camera_name}'")
            return False

        if not payload["project_id"]:
            logger.error(f"No project ID found for camera '{camera_name}'")
            return False

        for attempt in range(RETRY_ATTEMPTS):
            try:
                logger.info(f"Adding VA plugin to camera '{camera_name}' (attempt {attempt + 1})")
                response = self.session.put(url, json=payload, timeout=REQUEST_TIMEOUT)
                response.raise_for_status()

                response_data = response.json()

                # Check if API returned success
                if response_data.get('code') != 0:
                    logger.error(f"API returned error for camera '{camera_name}': {response_data.get('message', 'Unknown error')}")
                    return False

                logger.info(f"Successfully added VA plugin {VA_PLUGIN_ID} to camera '{camera_name}'")
                return True

            except requests.exceptions.RequestException as e:
                logger.warning(f"Failed to update camera '{camera_name}' (attempt {attempt + 1}): {e}")
                if attempt < RETRY_ATTEMPTS - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff

        logger.error(f"Failed to update camera '{camera_name}' after {RETRY_ATTEMPTS} attempts")
        return False

    def add_va_to_all_cameras(self) -> bool:
        """
        Add VA plugin to all load test cameras

        Returns:
            True if all cameras were updated successfully, False otherwise
        """
        logger.info(f"Starting VA plugin addition for load test cameras")
        logger.info(f"VA Plugin ID to add: {VA_PLUGIN_ID}")

        # Step 1: Login to get bearer token
        if not self.login():
            logger.error("Failed to login - cannot proceed with VA plugin addition")
            return False

        # Step 2: Get all cameras
        all_cameras = self.get_all_cameras()
        if not all_cameras:
            logger.error("No cameras found or failed to fetch cameras")
            return False

        # Step 3: Filter to load test cameras
        load_test_cameras = self.filter_load_test_cameras(all_cameras)
        if not load_test_cameras:
            logger.warning("No load test cameras found")
            return True

        # Step 4: Add VA plugin to each camera
        successful_updates = 0
        failed_updates = []

        for i, camera in enumerate(load_test_cameras, 1):
            camera_name = camera.get('name', f'Camera {i}')
            current_va_plugins = camera.get('va_plugins', [])
            current_va_count = len(current_va_plugins)

            logger.info(f"Processing camera {i}/{len(load_test_cameras)}: {camera_name} (current VA plugins: {current_va_count})")

            if self.add_va_plugin_to_camera(camera):
                successful_updates += 1
            else:
                failed_updates.append(camera_name)

            # Small delay between updates to avoid overwhelming the API
            if i < len(load_test_cameras):
                time.sleep(DELAY_BETWEEN_REQUESTS)

        # Summary
        logger.info(f"VA plugin addition complete: {successful_updates}/{len(load_test_cameras)} cameras updated successfully")

        if failed_updates:
            logger.warning(f"Failed to update cameras: {failed_updates}")
            return False

        logger.info(f"All load test cameras now have VA plugin {VA_PLUGIN_ID}!")
        return True

    def cleanup(self):
        """Clean up resources"""
        self.session.close()


def main():
    """Main execution function"""
    manager = VAPluginManager()

    try:
        success = manager.add_va_to_all_cameras()
        if success:
            logger.info("VA plugin addition completed successfully!")
            print(f"\n✅ VA plugin addition complete! All load test cameras now have VA plugin {VA_PLUGIN_ID}")
            print(f"📹 Check the logs for detailed information about each camera update")
        else:
            logger.error("VA plugin addition failed!")
            print(f"\n❌ VA plugin addition failed! Check the logs for details.")
            print("Some cameras may not have been updated.")
            return 1

    except KeyboardInterrupt:
        logger.info("VA plugin addition interrupted by user")
        print("\n⚠️  VA plugin addition interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error during VA plugin addition: {e}")
        print(f"\n💥 Unexpected error: {e}")
        return 1
    finally:
        manager.cleanup()

    return 0


if __name__ == "__main__":
    exit(main())