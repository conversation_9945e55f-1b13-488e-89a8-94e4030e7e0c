#!/usr/bin/env python3
"""
Performs setup for load test
Final aim: Deploy 50 agents with 10 cameras each

Setup phase:
1. <PERSON><PERSON> into account and receive bearer token
2. Create an agent via API call
3. Receive response with agent api key
4. Store api key in apikeys.txt file (one key per line)
5. In the agent, create a camera via API call
6. Repeat 5 for 10 cameras
7. Repeat 1-6 for 50 agents

Load test phase:
1. Get SAS token manually via Azure Storage Explorer
2. Upload updated assign-agent.sh, agent-linux-amd64, and apikeys.txt to storage container
2. Manually upload deploy-vmss.sh to Azure Cloud Shell
3. Run deploy-vmss.sh to deploy agents to VMSS
4. Monitor load test results
"""

"""
Example payload to POST /token?grant_type=password
{
  "email": "<EMAIL>",
  "password": "qwe123"
}

Example response from POST /token?grant_type=password
{
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.clo210ebset9Z4gYKJi7MjvjtKSsSwFxrHVry8Mg2Vw",
    "token_type": "bearer",
    "expires_in": 3600,
    "expires_at": **********,
    "refresh_token": "_DwAP1irXHEbMEDEl099fQ",
    "user": {
        "id": "cd359b63-1711-4033-bd5c-123b491139ab",
        "aud": "",
        "role": "",
        "email": "<EMAIL>",
        "email_confirmed_at": "2025-05-20T06:39:02.748051Z",
        "phone": "",
        "confirmed_at": "2025-05-20T06:39:02.748051Z",
        "recovery_sent_at": "2025-07-06T10:39:01.601728Z",
        "last_sign_in_at": "2025-07-16T03:47:04.745039102Z",
        "app_metadata": {
            "provider": "email",
            "providers": [
                "email"
            ]
        },
        "user_metadata": {
            "email": "<EMAIL>",
            "email_verified": false,
            "name": "admin",
            "phone_verified": false,
            "sub": "cd359b63-1711-4033-bd5c-123b491139ab"
        },
        "identities": [
            {
                "identity_id": "f587c0c8-1e32-48e4-b3d8-201eaee54c21",
                "id": "cd359b63-1711-4033-bd5c-123b491139ab",
                "user_id": "cd359b63-1711-4033-bd5c-123b491139ab",
                "identity_data": {
                    "email": "<EMAIL>",
                    "email_verified": false,
                    "name": "admin",
                    "phone_verified": false,
                    "sub": "cd359b63-1711-4033-bd5c-123b491139ab"
                },
                "provider": "email",
                "last_sign_in_at": "2025-05-20T06:39:02.725878Z",
                "created_at": "2025-05-20T06:39:02.725936Z",
                "updated_at": "2025-05-20T06:39:02.725936Z",
                "email": "<EMAIL>"
            }
        ],
        "created_at": "2025-05-20T06:39:02.632414Z",
        "updated_at": "2025-07-16T03:47:04.777314Z",
        "is_anonymous": false
    }
}

Example payload to POST /agents
{"name":"aName","provider":"aProvider","project_id":"e10222a3-032a-4784-a4dd-d0cbfd4f17fb"}

Example response from POST /agents
{
    "code": 0,
    "message": "Success",
    "data": {
        "id": "ea495df1-3a60-4d31-9fdd-c92a93a2f2f3",
        "name": "654654",
        "provider": "546546",
        "class": "",
        "status": "Offline",
        "api_key": "JCpfZ5WoIH_9dbSFs_ldeJGJkqmiF0HUdCvF",
        "project": null,
        "api_keys": [],
        "cameras": [],
        "created_at": "2025-07-16T03:29:23.196325458Z",
        "updated_at": "2025-07-16T03:29:23.196325458Z"
    }
}

Example payload to POST /cameras
{"agent_id":"someID","name":"someName","project_id":"e10222a3-032a-4784-a4dd-d0cbfd4f17fb","rtsp":"someRTSP","tags":[],"va_plugin_ids":[]}

Example response from POST /cameras
{
    "code": 0,
    "message": "Success",
    "data": {
        "id": "f4948325-67a5-4774-8f3f-cfbf2eefa7d8",
        "name": "213",
        "rtsp": "213",
        "notes": "",
        "retention": "12 months",
        "tags": [],
        "status": "Offline",
        "recording": "On",
        "va_plugins": [],
        "created_at": "2025-07-16T03:30:30.287941832Z",
        "updated_at": "2025-07-16T03:30:30.287941832Z"
    }
}

Example payload to PUT /cameras/{id}
{
    "name": "",
    "rtsp": "",
    "tags": [],
    "notes": "",
    "recording": "",
    "agent_id": "",
    "project_id": "",
    "va_plugin_ids": [],
    "remove_tag_ids": [],
    "remove_va_plugin_ids": []
}
{
    "name": "newName",
    "rtsp": "rtsp://localhost:8554/mystream",
    "tags": [],
    "notes": "",
    "recording": "Off",
    "agent_id": "7701fc81-9be3-456c-8556-8b83141843a0",
    "project_id": "657e63e7-1e56-4ba1-8c12-ec8e534dcd87"
}
"""

import requests
import time
import logging
from typing import Dict, Optional
from pathlib import Path

# Configuration
API_BASE_URL = "https://api.volks-stg.knizsoft.com/api/v1"
# API_BASE_URL = "https://api.volks-dev.knizsoft.com/api/v1"
API_AGENT = "/agents" # POST
API_CAMERA = "/cameras" # POST

# Login Info
LOGIN_ENDPOINT="https://auth.volks-stg.knizsoft.com/token?grant_type=password"
# LOGIN_ENDPOINT="https://auth.volks-dev.knizsoft.com/token?grant_type=password"
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="123456aA@"

# Values for agent payload
AGENT_BASE_NAME = "loadtest-agent-" # Naming sceheme: loadtest-agentv2-001
AGENT_PROJECT_ID="e10222a3-032a-4784-a4dd-d0cbfd4f17fb" # load-test project on stg
# AGENT_PROJECT_ID="c27e395c-09a7-433d-a6bc-c3262604cc87" # load-test project on dev
AGENT_PROVIDE="dev"

# Values for camera payload
CAMERA_BASE_NAME = "load-test-camera-" # Naming scheme: load-test-camera-<agentname>-001
CAMERA_RTSP_BASE_URL = "rtsp://172.16.0.9:8554/stream"  # Will be appended with stream number

NUM_AGENTS = 10
CAMERAS_PER_AGENT = 5
API_KEYS_FILE = "apikeys.txt"
AGENT_IDS_FILE = "agent_ids.txt"
REQUEST_TIMEOUT = 30
RETRY_ATTEMPTS = 3
DELAY_BETWEEN_REQUESTS = 0.5  # seconds

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('load_test_setup.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class LoadTestSetup:
    """Handles the setup phase of the load test"""

    def __init__(self, api_base_url: str = API_BASE_URL):
        self.api_base_url = api_base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'LoadTestSetup/1.0'
        })
        self.bearer_token = None

    def login(self) -> bool:
        """
        Login to get bearer token for authentication

        Returns:
            True if login successful, False otherwise
        """
        payload = {
            "email": ADMIN_EMAIL,
            "password": ADMIN_PASSWORD
        }

        for attempt in range(RETRY_ATTEMPTS):
            try:
                logger.info(f"Attempting login (attempt {attempt + 1})")
                response = self.session.post(LOGIN_ENDPOINT, json=payload, timeout=REQUEST_TIMEOUT)
                response.raise_for_status()

                login_data = response.json()

                # Extract access token
                access_token = login_data.get('access_token')
                if not access_token:
                    logger.error("No access token returned from login")
                    return False

                self.bearer_token = access_token

                # Update session headers with bearer token
                self.session.headers.update({
                    'Authorization': f'Bearer {self.bearer_token}'
                })

                # Log token expiry info
                expires_in = login_data.get('expires_in', 'unknown')
                logger.info(f"Login successful! Token expires in {expires_in} seconds")
                return True

            except requests.exceptions.RequestException as e:
                logger.warning(f"Login failed (attempt {attempt + 1}): {e}")
                if attempt < RETRY_ATTEMPTS - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff

        logger.error(f"Login failed after {RETRY_ATTEMPTS} attempts")
        return False

    def create_agent(self, agent_name: str) -> Optional[Dict]:
        """
        Create an agent via API call

        Args:
            agent_name: Name for the agent

        Returns:
            Dict containing agent info including API key, or None if failed
        """
        url = f"{self.api_base_url}{API_AGENT}"
        payload = {
            "name": agent_name,
            "provider": AGENT_PROVIDE,
            "project_id": AGENT_PROJECT_ID
        }

        for attempt in range(RETRY_ATTEMPTS):
            try:
                logger.info(f"Creating agent '{agent_name}' (attempt {attempt + 1})")
                response = self.session.post(url, json=payload, timeout=REQUEST_TIMEOUT)
                response.raise_for_status()

                response_data = response.json()

                # Check if API returned success
                if response_data.get('code') != 0:
                    logger.error(f"API returned error for agent '{agent_name}': {response_data.get('message', 'Unknown error')}")
                    return None

                agent_data = response_data.get('data')
                if not agent_data:
                    logger.error(f"No data returned for agent '{agent_name}'")
                    return None

                logger.info(f"Successfully created agent '{agent_name}' with ID: {agent_data.get('id')}")
                return agent_data

            except requests.exceptions.RequestException as e:
                logger.warning(f"Failed to create agent '{agent_name}' (attempt {attempt + 1}): {e}")
                if attempt < RETRY_ATTEMPTS - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff

        logger.error(f"Failed to create agent '{agent_name}' after {RETRY_ATTEMPTS} attempts")
        return None

    def create_camera(self, agent_id: str, camera_name: str, stream_number: int) -> Optional[Dict]:
        """
        Create a camera for an agent via API call

        Args:
            agent_id: ID of the agent to create camera for
            camera_name: Name for the camera
            stream_number: Stream number for RTSP URL (1-5)

        Returns:
            Dict containing camera info, or None if failed
        """
        url = f"{self.api_base_url}{API_CAMERA}"
        # Note: Using bearer token from session headers, not agent API key

        # Generate incremental RTSP URL (stream1, stream2, etc.)
        rtsp_url = f"{CAMERA_RTSP_BASE_URL}{stream_number}"

        payload = {
            "agent_id": agent_id,
            "name": camera_name,
            "project_id": AGENT_PROJECT_ID,
            "rtsp": rtsp_url,
            "tags": [],
            "va_plugin_ids": []
        }

        for attempt in range(RETRY_ATTEMPTS):
            try:
                logger.info(f"Creating camera '{camera_name}' (attempt {attempt + 1})")
                response = self.session.post(url, json=payload, timeout=REQUEST_TIMEOUT)
                response.raise_for_status()

                response_data = response.json()

                # Check if API returned success
                if response_data.get('code') != 0:
                    logger.error(f"API returned error for camera '{camera_name}': {response_data.get('message', 'Unknown error')}")
                    return None

                camera_data = response_data.get('data')
                if not camera_data:
                    logger.error(f"No data returned for camera '{camera_name}'")
                    return None

                logger.info(f"Successfully created camera '{camera_name}' with ID: {camera_data.get('id')}")
                return camera_data

            except requests.exceptions.RequestException as e:
                logger.warning(f"Failed to create camera '{camera_name}' (attempt {attempt + 1}): {e}")
                if attempt < RETRY_ATTEMPTS - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff

        logger.error(f"Failed to create camera '{camera_name}' after {RETRY_ATTEMPTS} attempts")
        return None

    def set_camera_recording_off(self, camera_id: str, camera_name: str, rtsp_url: str, agent_id: str, project_id: str) -> bool:
        """
        Turn off recording for a specific camera via API call

        Args:
            camera_id: ID of the camera to update
            camera_name: Name of the camera (for logging)
            rtsp_url: RTSP URL of the camera
            agent_id: ID of the agent owning the camera
            project_id: ID of the project

        Returns:
            True if recording was turned off successfully, False otherwise
        """
        url = f"{self.api_base_url}{API_CAMERA}/{camera_id}"
        payload = {
            "name": camera_name,
            "rtsp": rtsp_url,
            "tags": [],
            "notes": "",
            "recording": "Off",
            "agent_id": agent_id,
            "project_id": project_id
        }

        for attempt in range(RETRY_ATTEMPTS):
            try:
                logger.info(f"Setting camera '{camera_id}' recording off (attempt {attempt + 1})")
                response = self.session.put(url, json=payload, timeout=REQUEST_TIMEOUT)
                response.raise_for_status()

                response_data = response.json()

                if response_data.get('code') != 0:
                    logger.error(f"API returned error for camera '{camera_name}': {response_data.get('message', 'Unknown error')}")
                    return False
                
                logger.info(f"Successfully set camera '{camera_name}' recording off")
                return True

            except requests.exceptions.RequestException as e:
                logger.warning(f"Failed to set camera '{camera_id}' recording off (attempt {attempt + 1}): {e}")
                if attempt < RETRY_ATTEMPTS - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff


    def save_api_key(self, api_key: str, filename: str = API_KEYS_FILE) -> bool:
        """
        Save API key to file (one key per line)

        Args:
            api_key: The API key to save
            filename: File to save to

        Returns:
            True if successful, False otherwise
        """
        try:
            with open(filename, 'a') as f:
                f.write(f"{api_key}\n")
            logger.info(f"Saved API key to {filename}")
            return True
        except Exception as e:
            logger.error(f"Failed to save API key to {filename}: {e}")
            return False

    def save_agent_id(self, agent_id: str, filename: str = AGENT_IDS_FILE) -> bool:
        """
        Save agent ID to file (one ID per line)

        Args:
            agent_id: The agent ID to save
            filename: File to save to

        Returns:
            True if successful, False otherwise
        """
        try:
            with open(filename, 'a') as f:
                f.write(f"{agent_id}\n")
            logger.info(f"Saved agent ID to {filename}")
            return True
        except Exception as e:
            logger.error(f"Failed to save agent ID to {filename}: {e}")
            return False

    def setup_agent_with_cameras(self, agent_index: int) -> bool:
        """
        Setup a single agent with multiple cameras

        Args:
            agent_index: Index of the agent (for naming)

        Returns:
            True if successful, False otherwise
        """
        agent_name = f"{AGENT_BASE_NAME}{agent_index:03d}"

        # Step 1: Create agent
        agent_data = self.create_agent(agent_name)
        if not agent_data:
            return False

        # Step 2: Extract API key and agent ID
        api_key = agent_data.get('api_key')
        agent_id = agent_data.get('id')

        if not api_key:
            logger.error(f"No API key returned for agent '{agent_name}'")
            return False

        if not agent_id:
            logger.error(f"No agent ID returned for agent '{agent_name}'")
            return False

        if not self.save_api_key(api_key):
            return False

        if not self.save_agent_id(agent_id):
            return False

        # Step 3: Create cameras for this agent
        cameras_created = 0
        for camera_index in range(CAMERAS_PER_AGENT):
            camera_name = f"{CAMERA_BASE_NAME}{agent_name}-{camera_index + 1:03d}"

            # Cycle through streams 1-5 (stream01, stream02, stream03, stream04, stream05)
            stream_number = (camera_index % 5) + 1

            camera_data = self.create_camera(agent_id, camera_name, stream_number)
            self.set_camera_recording_off(camera_data.get('id'), camera_data.get('name'), camera_data.get('rtsp'), agent_id, AGENT_PROJECT_ID)

            if camera_data:
                cameras_created += 1
                logger.info(f"Camera '{camera_name}' created with RTSP stream{stream_number}")

            # Small delay between camera creation requests
            time.sleep(DELAY_BETWEEN_REQUESTS)

        success = cameras_created == CAMERAS_PER_AGENT
        if success:
            logger.info(f"Successfully setup agent '{agent_name}' with {cameras_created} cameras")
        else:
            logger.warning(f"Agent '{agent_name}' setup incomplete: {cameras_created}/{CAMERAS_PER_AGENT} cameras created")

        return success

    def run_setup(self) -> bool:
        """
        Run the complete setup process for all agents

        Returns:
            True if all agents were setup successfully, False otherwise
        """
        logger.info(f"Starting load test setup: {NUM_AGENTS} agents with {CAMERAS_PER_AGENT} cameras each")

        # Step 1: Login to get bearer token
        if not self.login():
            logger.error("Failed to login - cannot proceed with setup")
            return False

        # Clear existing files
        try:
            Path(API_KEYS_FILE).unlink(missing_ok=True)
            logger.info(f"Cleared existing {API_KEYS_FILE}")
        except Exception as e:
            logger.warning(f"Could not clear {API_KEYS_FILE}: {e}")

        try:
            Path(AGENT_IDS_FILE).unlink(missing_ok=True)
            logger.info(f"Cleared existing {AGENT_IDS_FILE}")
        except Exception as e:
            logger.warning(f"Could not clear {AGENT_IDS_FILE}: {e}")

        successful_agents = 0
        failed_agents = []

        for agent_index in range(1, NUM_AGENTS + 1):
            logger.info(f"Setting up agent {agent_index}/{NUM_AGENTS}")

            if self.setup_agent_with_cameras(agent_index):
                successful_agents += 1
            else:
                failed_agents.append(agent_index)

            # Small delay between agent setup to avoid overwhelming the API
            if agent_index < NUM_AGENTS:
                time.sleep(DELAY_BETWEEN_REQUESTS)

        # Summary
        logger.info(f"Setup complete: {successful_agents}/{NUM_AGENTS} agents created successfully")

        if failed_agents:
            logger.warning(f"Failed agents: {failed_agents}")
            return False

        logger.info(f"All API keys saved to {API_KEYS_FILE}")
        logger.info(f"All agent IDs saved to {AGENT_IDS_FILE}")
        return True

    def cleanup(self):
        """Clean up resources"""
        self.session.close()


def main():
    """Main execution function"""
    setup = LoadTestSetup()

    try:
        success = setup.run_setup()
        if success:
            logger.info("Load test setup completed successfully!")
            print(f"\n✅ Setup complete! API keys saved to {API_KEYS_FILE}")
            print(f"📊 Created {NUM_AGENTS} agents with {CAMERAS_PER_AGENT} cameras each")
            print(f"🆔 Agent IDs saved to {AGENT_IDS_FILE} for cleanup")
            print("\nNext steps:")
            print("1. Get SAS token via Azure Storage Explorer")
            print("2. Upload assign-agent.sh, agent-linux-amd64, and apikeys.txt to storage container")
            print("3. Upload deploy-vmss.sh to Azure Cloud Shell")
            print("4. Run deploy-vmss.sh to deploy agents to VMSS")
        else:
            logger.error("Load test setup failed!")
            print("\n❌ Setup failed! Check the logs for details.")
            return 1

    except KeyboardInterrupt:
        logger.info("Setup interrupted by user")
        print("\n⚠️  Setup interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error during setup: {e}")
        print(f"\n💥 Unexpected error: {e}")
        return 1
    finally:
        setup.cleanup()

    return 0


if __name__ == "__main__":
    exit(main())
