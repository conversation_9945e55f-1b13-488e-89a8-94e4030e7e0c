#!/bin/bash

# Configuration
RESOURCE_GROUP="volks-loadtest-rg"
STORAGE_ACCOUNT="volksloadtestsa"
CONTAINER_NAME="volks-loadtest-blob"
SCRIPT_NAME="assign-agent.sh"

# NUM_INSTANCES=$1

if [ $# -ne 3 ]; then
    echo "Usage: $0 <START_IDX> <END_IDX> <ENV>"
    exit 1
fi

START_IDX=$1 # Starts at 1
END_IDX=$2
ENV=$3

#ENCODED_SAS_TOKEN=$1
#CONTAINER_SAS_TOKEN=$(echo "$ENCODED_SAS_TOKEN" | base64 -d)

CONTAINER_SAS_TOKEN="sp=r&st=2025-07-25T00:49:02Z&se=2025-07-25T09:04:02Z&spr=https&sv=2024-11-04&sr=c&sig=M9hzjB%2Bo28o5CI0cr0Brc9qZtV30r39aXEdhTG7TNxk%3D"
ENCODED_SAS_TOKEN=$(echo -n "$CONTAINER_SAS_TOKEN" | base64 -w 0)

# Construct the correct URL with the specific file
SAS_URL="https://${STORAGE_ACCOUNT}.blob.core.windows.net/${CONTAINER_NAME}/${SCRIPT_NAME}?${CONTAINER_SAS_TOKEN}"

index=0
for VM in $(az vm list --resource-group $RESOURCE_GROUP --query "[?contains(name, 'volks-vmss')].name" -o tsv); do
    index=$((index+1));
    
    if [ "$END_IDX" != "" ] && [ "$index" -gt "$END_IDX" ]; then
        break
    fi
    
    if [ "$START_IDX" != "" ] && [ "$index" -lt "$START_IDX" ]; then
        continue
    fi
    
    # Create temporary settings file to avoid JSON escaping issues
    cat > settings.json << EOF
{
  "fileUris": ["$SAS_URL"]
}
EOF
    
    echo "Deploying to $VM with index $index"
    az vm extension set \
      --resource-group $RESOURCE_GROUP \
      --name CustomScript \
      --vm-name $VM \
      --force-update \
      --publisher Microsoft.Azure.Extensions \
      --settings settings.json \
      --protected-settings "{\"commandToExecute\": \"bash $SCRIPT_NAME $index $ENCODED_SAS_TOKEN $ENV\"}"
    
    # Clean up temp file
    rm -f settings.json
done
