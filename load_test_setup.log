2025-07-22 17:52:25,309 - INFO - Starting load test setup: 10 agents with 5 cameras each
2025-07-22 17:52:25,309 - INFO - Attempting login (attempt 1)
2025-07-22 17:52:25,558 - INFO - Login successful! Token expires in 3600 seconds
2025-07-22 17:52:25,558 - INFO - Cleared existing apikeys.txt
2025-07-22 17:52:25,558 - INFO - Cleared existing agent_ids.txt
2025-07-22 17:52:25,558 - INFO - Setting up agent 1/10
2025-07-22 17:52:25,558 - INFO - Creating agent 'loadtest-agent-001' (attempt 1)
2025-07-22 17:52:25,705 - INFO - Successfully created agent 'loadtest-agent-001' with ID: 0d3af672-1382-472d-ad9f-ebc5798b7f25
2025-07-22 17:52:25,706 - INFO - Saved API key to apikeys.txt
2025-07-22 17:52:25,707 - INFO - Saved agent ID to agent_ids.txt
2025-07-22 17:52:25,707 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-001' (attempt 1)
2025-07-22 17:52:25,785 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-001' with ID: 4f9433a6-af82-407b-b8ba-5eb00bc9cc87
2025-07-22 17:52:25,786 - INFO - Camera 'load-test-camera-loadtest-agent-001-001' created with RTSP stream1
2025-07-22 17:52:26,286 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-002' (attempt 1)
2025-07-22 17:52:26,353 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-002' with ID: 9c073317-7be9-4fc5-a39f-2e305a58d7cb
2025-07-22 17:52:26,353 - INFO - Camera 'load-test-camera-loadtest-agent-001-002' created with RTSP stream2
2025-07-22 17:52:26,853 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-003' (attempt 1)
2025-07-22 17:52:26,930 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-003' with ID: 9e41e29e-854d-438a-b823-9c46fde00371
2025-07-22 17:52:26,930 - INFO - Camera 'load-test-camera-loadtest-agent-001-003' created with RTSP stream3
2025-07-22 17:52:27,431 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-004' (attempt 1)
2025-07-22 17:52:27,506 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-004' with ID: 8e9fba19-f19a-41b2-942a-346084c44483
2025-07-22 17:52:27,506 - INFO - Camera 'load-test-camera-loadtest-agent-001-004' created with RTSP stream4
2025-07-22 17:52:28,007 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-005' (attempt 1)
2025-07-22 17:52:28,083 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-005' with ID: 0bd9b5af-117d-4875-ad27-d91a6fa1ed91
2025-07-22 17:52:28,083 - INFO - Camera 'load-test-camera-loadtest-agent-001-005' created with RTSP stream5
2025-07-22 17:52:28,584 - INFO - Successfully setup agent 'loadtest-agent-001' with 5 cameras
2025-07-22 17:52:29,084 - INFO - Setting up agent 2/10
2025-07-22 17:52:29,085 - INFO - Creating agent 'loadtest-agent-002' (attempt 1)
2025-07-22 17:52:29,172 - INFO - Successfully created agent 'loadtest-agent-002' with ID: ff8b81e9-2617-4392-82ab-d4bd15630d40
2025-07-22 17:52:29,173 - INFO - Saved API key to apikeys.txt
2025-07-22 17:52:29,173 - INFO - Saved agent ID to agent_ids.txt
2025-07-22 17:52:29,173 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-001' (attempt 1)
2025-07-22 17:52:29,257 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-001' with ID: dbaac8b7-eca1-46c1-b3a1-98b9494a08f0
2025-07-22 17:52:29,257 - INFO - Camera 'load-test-camera-loadtest-agent-002-001' created with RTSP stream1
2025-07-22 17:52:29,758 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-002' (attempt 1)
2025-07-22 17:52:30,101 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-002' with ID: 31a241f1-c0ee-4665-98ab-67613bd81bc4
2025-07-22 17:52:30,102 - INFO - Camera 'load-test-camera-loadtest-agent-002-002' created with RTSP stream2
2025-07-22 17:52:30,602 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-003' (attempt 1)
2025-07-22 17:52:30,665 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-003' with ID: 10bc713b-4487-4d22-9ddc-730e2316951a
2025-07-22 17:52:30,665 - INFO - Camera 'load-test-camera-loadtest-agent-002-003' created with RTSP stream3
2025-07-22 17:52:31,166 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-004' (attempt 1)
2025-07-22 17:52:31,236 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-004' with ID: daa655c8-44aa-4a2f-9290-a9e30225200f
2025-07-22 17:52:31,236 - INFO - Camera 'load-test-camera-loadtest-agent-002-004' created with RTSP stream4
2025-07-22 17:52:31,737 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-005' (attempt 1)
2025-07-22 17:52:31,819 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-005' with ID: a565fb0a-6265-46d9-8b75-210c7f10b3a4
2025-07-22 17:52:31,819 - INFO - Camera 'load-test-camera-loadtest-agent-002-005' created with RTSP stream5
2025-07-22 17:52:32,320 - INFO - Successfully setup agent 'loadtest-agent-002' with 5 cameras
2025-07-22 17:52:32,821 - INFO - Setting up agent 3/10
2025-07-22 17:52:32,821 - INFO - Creating agent 'loadtest-agent-003' (attempt 1)
2025-07-22 17:52:32,889 - INFO - Successfully created agent 'loadtest-agent-003' with ID: 253ef95f-e2a2-4aee-8e2b-1a7e97e616cc
2025-07-22 17:52:32,890 - INFO - Saved API key to apikeys.txt
2025-07-22 17:52:32,890 - INFO - Saved agent ID to agent_ids.txt
2025-07-22 17:52:32,890 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-001' (attempt 1)
2025-07-22 17:52:32,964 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-001' with ID: cf4f9f39-1c1e-4dfe-8a4a-71a58e7ca4af
2025-07-22 17:52:32,964 - INFO - Camera 'load-test-camera-loadtest-agent-003-001' created with RTSP stream1
2025-07-22 17:52:33,464 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-002' (attempt 1)
2025-07-22 17:52:33,529 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-002' with ID: b1ea8081-eefd-4131-ba63-acedd69a2413
2025-07-22 17:52:33,529 - INFO - Camera 'load-test-camera-loadtest-agent-003-002' created with RTSP stream2
2025-07-22 17:52:34,030 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-003' (attempt 1)
2025-07-22 17:52:34,094 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-003' with ID: 4a3b7fde-33a2-4998-9d13-5015473d1e58
2025-07-22 17:52:34,094 - INFO - Camera 'load-test-camera-loadtest-agent-003-003' created with RTSP stream3
2025-07-22 17:52:34,595 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-004' (attempt 1)
2025-07-22 17:52:34,655 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-004' with ID: 73d112a9-9436-4a98-b08b-ea0dc1cab482
2025-07-22 17:52:34,655 - INFO - Camera 'load-test-camera-loadtest-agent-003-004' created with RTSP stream4
2025-07-22 17:52:35,156 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-005' (attempt 1)
2025-07-22 17:52:35,221 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-005' with ID: 517ed34e-e78d-4e5e-b701-05a30f18aba0
2025-07-22 17:52:35,221 - INFO - Camera 'load-test-camera-loadtest-agent-003-005' created with RTSP stream5
2025-07-22 17:52:35,722 - INFO - Successfully setup agent 'loadtest-agent-003' with 5 cameras
2025-07-22 17:52:36,222 - INFO - Setting up agent 4/10
2025-07-22 17:52:36,222 - INFO - Creating agent 'loadtest-agent-004' (attempt 1)
2025-07-22 17:52:36,278 - INFO - Successfully created agent 'loadtest-agent-004' with ID: 291ae770-486e-4542-99e7-260b09b81476
2025-07-22 17:52:36,278 - INFO - Saved API key to apikeys.txt
2025-07-22 17:52:36,278 - INFO - Saved agent ID to agent_ids.txt
2025-07-22 17:52:36,279 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-001' (attempt 1)
2025-07-22 17:52:36,343 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-001' with ID: 91144897-987f-4e95-88ea-4b65d91f84e0
2025-07-22 17:52:36,343 - INFO - Camera 'load-test-camera-loadtest-agent-004-001' created with RTSP stream1
2025-07-22 17:52:36,844 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-002' (attempt 1)
2025-07-22 17:52:36,907 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-002' with ID: c63a0602-fcb8-4b0d-8b4c-fad6aaf69ba5
2025-07-22 17:52:36,908 - INFO - Camera 'load-test-camera-loadtest-agent-004-002' created with RTSP stream2
2025-07-22 17:52:37,408 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-003' (attempt 1)
2025-07-22 17:52:37,473 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-003' with ID: 8a4cc065-864b-40e6-b77c-8dacb10c8d9b
2025-07-22 17:52:37,473 - INFO - Camera 'load-test-camera-loadtest-agent-004-003' created with RTSP stream3
2025-07-22 17:52:37,974 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-004' (attempt 1)
2025-07-22 17:52:38,046 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-004' with ID: 83a03cb4-6664-4a38-b61f-0af8fa97d59c
2025-07-22 17:52:38,046 - INFO - Camera 'load-test-camera-loadtest-agent-004-004' created with RTSP stream4
2025-07-22 17:52:38,546 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-005' (attempt 1)
2025-07-22 17:52:38,622 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-005' with ID: f4ec651f-e307-4ab7-b459-c50321fd050f
2025-07-22 17:52:38,622 - INFO - Camera 'load-test-camera-loadtest-agent-004-005' created with RTSP stream5
2025-07-22 17:52:39,123 - INFO - Successfully setup agent 'loadtest-agent-004' with 5 cameras
2025-07-22 17:52:39,624 - INFO - Setting up agent 5/10
2025-07-22 17:52:39,624 - INFO - Creating agent 'loadtest-agent-005' (attempt 1)
2025-07-22 17:52:39,691 - INFO - Successfully created agent 'loadtest-agent-005' with ID: 0043317d-ef76-423b-aa0f-e87aa90bb06f
2025-07-22 17:52:39,691 - INFO - Saved API key to apikeys.txt
2025-07-22 17:52:39,691 - INFO - Saved agent ID to agent_ids.txt
2025-07-22 17:52:39,692 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-001' (attempt 1)
2025-07-22 17:52:39,753 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-001' with ID: dc59ad93-c39b-4c76-b7db-79b66ff271d8
2025-07-22 17:52:39,753 - INFO - Camera 'load-test-camera-loadtest-agent-005-001' created with RTSP stream1
2025-07-22 17:52:40,254 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-002' (attempt 1)
2025-07-22 17:52:40,325 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-002' with ID: d9cdbfcb-c445-46c6-951a-31a63e01d4aa
2025-07-22 17:52:40,325 - INFO - Camera 'load-test-camera-loadtest-agent-005-002' created with RTSP stream2
2025-07-22 17:52:40,825 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-003' (attempt 1)
2025-07-22 17:52:40,891 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-003' with ID: 1a2882cc-95d3-4324-a18e-92f7ecb73bd2
2025-07-22 17:52:40,891 - INFO - Camera 'load-test-camera-loadtest-agent-005-003' created with RTSP stream3
2025-07-22 17:52:41,392 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-004' (attempt 1)
2025-07-22 17:52:41,452 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-004' with ID: bb359894-cf22-4e65-bb23-697f336fec70
2025-07-22 17:52:41,452 - INFO - Camera 'load-test-camera-loadtest-agent-005-004' created with RTSP stream4
2025-07-22 17:52:41,953 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-005' (attempt 1)
2025-07-22 17:52:42,021 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-005' with ID: 7ca0411c-45dd-41b6-925f-527e63ffacee
2025-07-22 17:52:42,021 - INFO - Camera 'load-test-camera-loadtest-agent-005-005' created with RTSP stream5
2025-07-22 17:52:42,522 - INFO - Successfully setup agent 'loadtest-agent-005' with 5 cameras
2025-07-22 17:52:43,023 - INFO - Setting up agent 6/10
2025-07-22 17:52:43,023 - INFO - Creating agent 'loadtest-agent-006' (attempt 1)
2025-07-22 17:52:43,091 - INFO - Successfully created agent 'loadtest-agent-006' with ID: 413c20bd-492b-4086-9fa3-d8ce7c6b3626
2025-07-22 17:52:43,092 - INFO - Saved API key to apikeys.txt
2025-07-22 17:52:43,092 - INFO - Saved agent ID to agent_ids.txt
2025-07-22 17:52:43,092 - INFO - Creating camera 'load-test-camera-loadtest-agent-006-001' (attempt 1)
2025-07-22 17:52:43,373 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-006-001' with ID: 9f12d4fb-a0e3-4657-8ddb-6c873b8665cf
2025-07-22 17:52:43,374 - INFO - Camera 'load-test-camera-loadtest-agent-006-001' created with RTSP stream1
2025-07-22 17:52:43,874 - INFO - Creating camera 'load-test-camera-loadtest-agent-006-002' (attempt 1)
2025-07-22 17:52:43,951 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-006-002' with ID: a394ee75-b7a8-4fb6-bcc8-80590b856cd8
2025-07-22 17:52:43,951 - INFO - Camera 'load-test-camera-loadtest-agent-006-002' created with RTSP stream2
2025-07-22 17:52:44,452 - INFO - Creating camera 'load-test-camera-loadtest-agent-006-003' (attempt 1)
2025-07-22 17:52:44,525 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-006-003' with ID: 51cc0430-0cc1-4cc8-b691-8a8191198cf8
2025-07-22 17:52:44,525 - INFO - Camera 'load-test-camera-loadtest-agent-006-003' created with RTSP stream3
2025-07-22 17:52:45,026 - INFO - Creating camera 'load-test-camera-loadtest-agent-006-004' (attempt 1)
2025-07-22 17:52:45,088 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-006-004' with ID: 4659d671-6479-4362-b266-eb23b9030554
2025-07-22 17:52:45,088 - INFO - Camera 'load-test-camera-loadtest-agent-006-004' created with RTSP stream4
2025-07-22 17:52:45,589 - INFO - Creating camera 'load-test-camera-loadtest-agent-006-005' (attempt 1)
2025-07-22 17:52:45,651 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-006-005' with ID: 58a5397f-33e8-43b4-a29a-1060bcc58b59
2025-07-22 17:52:45,651 - INFO - Camera 'load-test-camera-loadtest-agent-006-005' created with RTSP stream5
2025-07-22 17:52:46,152 - INFO - Successfully setup agent 'loadtest-agent-006' with 5 cameras
2025-07-22 17:52:46,652 - INFO - Setting up agent 7/10
2025-07-22 17:52:46,653 - INFO - Creating agent 'loadtest-agent-007' (attempt 1)
2025-07-22 17:52:46,719 - INFO - Successfully created agent 'loadtest-agent-007' with ID: 85d2c0f2-c132-4ab6-b0b2-e04124783730
2025-07-22 17:52:46,719 - INFO - Saved API key to apikeys.txt
2025-07-22 17:52:46,719 - INFO - Saved agent ID to agent_ids.txt
2025-07-22 17:52:46,720 - INFO - Creating camera 'load-test-camera-loadtest-agent-007-001' (attempt 1)
2025-07-22 17:52:46,793 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-007-001' with ID: 85bf356a-9765-4532-9d8b-d55cb54b83b5
2025-07-22 17:52:46,793 - INFO - Camera 'load-test-camera-loadtest-agent-007-001' created with RTSP stream1
2025-07-22 17:52:47,294 - INFO - Creating camera 'load-test-camera-loadtest-agent-007-002' (attempt 1)
2025-07-22 17:52:47,351 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-007-002' with ID: 6551577c-54d9-4baa-95f3-1df382d23659
2025-07-22 17:52:47,351 - INFO - Camera 'load-test-camera-loadtest-agent-007-002' created with RTSP stream2
2025-07-22 17:52:47,852 - INFO - Creating camera 'load-test-camera-loadtest-agent-007-003' (attempt 1)
2025-07-22 17:52:47,910 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-007-003' with ID: 34d30013-3f98-4bf6-b19a-5029384e24e4
2025-07-22 17:52:47,910 - INFO - Camera 'load-test-camera-loadtest-agent-007-003' created with RTSP stream3
2025-07-22 17:52:48,411 - INFO - Creating camera 'load-test-camera-loadtest-agent-007-004' (attempt 1)
2025-07-22 17:52:48,468 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-007-004' with ID: 7a3a0e1b-337f-4db8-ae0b-95beaa219d4d
2025-07-22 17:52:48,468 - INFO - Camera 'load-test-camera-loadtest-agent-007-004' created with RTSP stream4
2025-07-22 17:52:48,969 - INFO - Creating camera 'load-test-camera-loadtest-agent-007-005' (attempt 1)
2025-07-22 17:52:49,045 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-007-005' with ID: e52a31da-ff07-4354-b009-abf3e4c15b2e
2025-07-22 17:52:49,045 - INFO - Camera 'load-test-camera-loadtest-agent-007-005' created with RTSP stream5
2025-07-22 17:52:49,546 - INFO - Successfully setup agent 'loadtest-agent-007' with 5 cameras
2025-07-22 17:52:50,047 - INFO - Setting up agent 8/10
2025-07-22 17:52:50,047 - INFO - Creating agent 'loadtest-agent-008' (attempt 1)
2025-07-22 17:52:50,110 - INFO - Successfully created agent 'loadtest-agent-008' with ID: 3763e3e9-b3a1-4416-ac19-9b188262eea7
2025-07-22 17:52:50,110 - INFO - Saved API key to apikeys.txt
2025-07-22 17:52:50,110 - INFO - Saved agent ID to agent_ids.txt
2025-07-22 17:52:50,110 - INFO - Creating camera 'load-test-camera-loadtest-agent-008-001' (attempt 1)
2025-07-22 17:52:50,173 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-008-001' with ID: 00a6a4c2-921d-486f-920f-45fcbd2bd83a
2025-07-22 17:52:50,173 - INFO - Camera 'load-test-camera-loadtest-agent-008-001' created with RTSP stream1
2025-07-22 17:52:50,674 - INFO - Creating camera 'load-test-camera-loadtest-agent-008-002' (attempt 1)
2025-07-22 17:52:50,735 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-008-002' with ID: 1c395057-6055-4a20-a9ab-906334ffdf1f
2025-07-22 17:52:50,735 - INFO - Camera 'load-test-camera-loadtest-agent-008-002' created with RTSP stream2
2025-07-22 17:52:51,236 - INFO - Creating camera 'load-test-camera-loadtest-agent-008-003' (attempt 1)
2025-07-22 17:52:51,314 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-008-003' with ID: cb44183b-426a-4a5e-9409-c24ece4fa01d
2025-07-22 17:52:51,314 - INFO - Camera 'load-test-camera-loadtest-agent-008-003' created with RTSP stream3
2025-07-22 17:52:51,815 - INFO - Creating camera 'load-test-camera-loadtest-agent-008-004' (attempt 1)
2025-07-22 17:52:51,877 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-008-004' with ID: 7d726ba9-5085-4747-bbbf-12a4a943b20c
2025-07-22 17:52:51,877 - INFO - Camera 'load-test-camera-loadtest-agent-008-004' created with RTSP stream4
2025-07-22 17:52:52,377 - INFO - Creating camera 'load-test-camera-loadtest-agent-008-005' (attempt 1)
2025-07-22 17:52:52,449 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-008-005' with ID: 68a358bd-39f8-49e9-877a-c055c2db2efa
2025-07-22 17:52:52,449 - INFO - Camera 'load-test-camera-loadtest-agent-008-005' created with RTSP stream5
2025-07-22 17:52:52,949 - INFO - Successfully setup agent 'loadtest-agent-008' with 5 cameras
2025-07-22 17:52:53,450 - INFO - Setting up agent 9/10
2025-07-22 17:52:53,450 - INFO - Creating agent 'loadtest-agent-009' (attempt 1)
2025-07-22 17:52:53,512 - INFO - Successfully created agent 'loadtest-agent-009' with ID: 7db7ffd3-bf0a-4442-830d-799162fcab64
2025-07-22 17:52:53,512 - INFO - Saved API key to apikeys.txt
2025-07-22 17:52:53,512 - INFO - Saved agent ID to agent_ids.txt
2025-07-22 17:52:53,512 - INFO - Creating camera 'load-test-camera-loadtest-agent-009-001' (attempt 1)
2025-07-22 17:52:53,575 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-009-001' with ID: 45c6da2f-ecb8-4865-8a00-07471e12fa75
2025-07-22 17:52:53,575 - INFO - Camera 'load-test-camera-loadtest-agent-009-001' created with RTSP stream1
2025-07-22 17:52:54,076 - INFO - Creating camera 'load-test-camera-loadtest-agent-009-002' (attempt 1)
2025-07-22 17:52:54,155 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-009-002' with ID: 87ce2b68-a837-4375-96ac-dd6b96c422b7
2025-07-22 17:52:54,155 - INFO - Camera 'load-test-camera-loadtest-agent-009-002' created with RTSP stream2
2025-07-22 17:52:54,655 - INFO - Creating camera 'load-test-camera-loadtest-agent-009-003' (attempt 1)
2025-07-22 17:52:54,722 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-009-003' with ID: 3f1dcf1c-38c6-4336-b5eb-601f2375241d
2025-07-22 17:52:54,722 - INFO - Camera 'load-test-camera-loadtest-agent-009-003' created with RTSP stream3
2025-07-22 17:52:55,223 - INFO - Creating camera 'load-test-camera-loadtest-agent-009-004' (attempt 1)
2025-07-22 17:52:55,289 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-009-004' with ID: 644c701c-5080-4659-8ffc-64608fa43944
2025-07-22 17:52:55,289 - INFO - Camera 'load-test-camera-loadtest-agent-009-004' created with RTSP stream4
2025-07-22 17:52:55,790 - INFO - Creating camera 'load-test-camera-loadtest-agent-009-005' (attempt 1)
2025-07-22 17:52:55,857 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-009-005' with ID: 54b15805-c8a3-45b6-b646-d9623738c408
2025-07-22 17:52:55,857 - INFO - Camera 'load-test-camera-loadtest-agent-009-005' created with RTSP stream5
2025-07-22 17:52:56,358 - INFO - Successfully setup agent 'loadtest-agent-009' with 5 cameras
2025-07-22 17:52:56,858 - INFO - Setting up agent 10/10
2025-07-22 17:52:56,859 - INFO - Creating agent 'loadtest-agent-010' (attempt 1)
2025-07-22 17:52:56,926 - INFO - Successfully created agent 'loadtest-agent-010' with ID: ab498b4b-e10e-4b65-b735-d24f6ba0c07d
2025-07-22 17:52:56,926 - INFO - Saved API key to apikeys.txt
2025-07-22 17:52:56,927 - INFO - Saved agent ID to agent_ids.txt
2025-07-22 17:52:56,927 - INFO - Creating camera 'load-test-camera-loadtest-agent-010-001' (attempt 1)
2025-07-22 17:52:57,008 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-010-001' with ID: 70e4e16a-e9e1-4c3f-875f-d955828dfcf5
2025-07-22 17:52:57,008 - INFO - Camera 'load-test-camera-loadtest-agent-010-001' created with RTSP stream1
2025-07-22 17:52:57,509 - INFO - Creating camera 'load-test-camera-loadtest-agent-010-002' (attempt 1)
2025-07-22 17:52:57,574 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-010-002' with ID: 150fe0b9-68a7-4e71-9932-e8d31ba75e79
2025-07-22 17:52:57,574 - INFO - Camera 'load-test-camera-loadtest-agent-010-002' created with RTSP stream2
2025-07-22 17:52:58,075 - INFO - Creating camera 'load-test-camera-loadtest-agent-010-003' (attempt 1)
2025-07-22 17:52:58,137 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-010-003' with ID: 2a4d36a7-6b83-43cc-bb17-052db6dc8333
2025-07-22 17:52:58,137 - INFO - Camera 'load-test-camera-loadtest-agent-010-003' created with RTSP stream3
2025-07-22 17:52:58,638 - INFO - Creating camera 'load-test-camera-loadtest-agent-010-004' (attempt 1)
2025-07-22 17:52:58,710 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-010-004' with ID: 2e305822-f02a-47ac-9da7-62f544a4cabb
2025-07-22 17:52:58,710 - INFO - Camera 'load-test-camera-loadtest-agent-010-004' created with RTSP stream4
2025-07-22 17:52:59,211 - INFO - Creating camera 'load-test-camera-loadtest-agent-010-005' (attempt 1)
2025-07-22 17:52:59,273 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-010-005' with ID: 11534a01-0633-4797-8233-bc11e0c386f2
2025-07-22 17:52:59,273 - INFO - Camera 'load-test-camera-loadtest-agent-010-005' created with RTSP stream5
2025-07-22 17:52:59,773 - INFO - Successfully setup agent 'loadtest-agent-010' with 5 cameras
2025-07-22 17:52:59,773 - INFO - Setup complete: 10/10 agents created successfully
2025-07-22 17:52:59,773 - INFO - All API keys saved to apikeys.txt
2025-07-22 17:52:59,774 - INFO - All agent IDs saved to agent_ids.txt
2025-07-22 17:52:59,774 - INFO - Load test setup completed successfully!
2025-07-23 15:07:20,695 - INFO - Starting load test setup: 1 agents with 5 cameras each
2025-07-23 15:07:20,695 - INFO - Attempting login (attempt 1)
2025-07-23 15:07:21,189 - INFO - Login successful! Token expires in 3600 seconds
2025-07-23 15:07:21,189 - INFO - Cleared existing apikeys.txt
2025-07-23 15:07:21,189 - INFO - Cleared existing agent_ids.txt
2025-07-23 15:07:21,190 - INFO - Setting up agent 1/1
2025-07-23 15:07:21,190 - INFO - Creating agent 'loadtest-agent-001' (attempt 1)
2025-07-23 15:07:21,331 - INFO - Successfully created agent 'loadtest-agent-001' with ID: 4f07f0a1-1d1e-4cb0-9155-7c5da36183f4
2025-07-23 15:07:21,332 - INFO - Saved API key to apikeys.txt
2025-07-23 15:07:21,333 - INFO - Saved agent ID to agent_ids.txt
2025-07-23 15:07:21,333 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-001' (attempt 1)
2025-07-23 15:07:21,402 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-001' with ID: baa5b338-1106-4f1e-9ff3-47c0633bbad6
2025-07-23 15:07:21,402 - INFO - Setting camera 'baa5b338-1106-4f1e-9ff3-47c0633bbad6' recording off (attempt 1)
2025-07-23 15:07:21,445 - WARNING - Failed to set camera 'baa5b338-1106-4f1e-9ff3-47c0633bbad6' recording off (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/baa5b338-1106-4f1e-9ff3-47c0633bbad6
2025-07-23 15:07:22,447 - INFO - Setting camera 'baa5b338-1106-4f1e-9ff3-47c0633bbad6' recording off (attempt 2)
2025-07-23 15:07:22,476 - WARNING - Failed to set camera 'baa5b338-1106-4f1e-9ff3-47c0633bbad6' recording off (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/baa5b338-1106-4f1e-9ff3-47c0633bbad6
2025-07-23 15:07:24,479 - INFO - Setting camera 'baa5b338-1106-4f1e-9ff3-47c0633bbad6' recording off (attempt 3)
2025-07-23 15:07:24,513 - WARNING - Failed to set camera 'baa5b338-1106-4f1e-9ff3-47c0633bbad6' recording off (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/baa5b338-1106-4f1e-9ff3-47c0633bbad6
2025-07-23 15:07:24,513 - INFO - Camera 'load-test-camera-loadtest-agent-001-001' created with RTSP stream1
2025-07-23 15:07:24,513 - INFO - Camera 'load-test-camera-loadtest-agent-001-001' created with RTSP stream1
2025-07-23 15:07:25,014 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-002' (attempt 1)
2025-07-23 15:07:25,082 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-002' with ID: 2fcd5a19-bd96-42c9-93cc-b8ef83115e1f
2025-07-23 15:07:25,082 - INFO - Setting camera '2fcd5a19-bd96-42c9-93cc-b8ef83115e1f' recording off (attempt 1)
2025-07-23 15:07:25,115 - WARNING - Failed to set camera '2fcd5a19-bd96-42c9-93cc-b8ef83115e1f' recording off (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/2fcd5a19-bd96-42c9-93cc-b8ef83115e1f
2025-07-23 15:07:26,116 - INFO - Setting camera '2fcd5a19-bd96-42c9-93cc-b8ef83115e1f' recording off (attempt 2)
2025-07-23 15:07:26,152 - WARNING - Failed to set camera '2fcd5a19-bd96-42c9-93cc-b8ef83115e1f' recording off (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/2fcd5a19-bd96-42c9-93cc-b8ef83115e1f
2025-07-23 15:07:28,155 - INFO - Setting camera '2fcd5a19-bd96-42c9-93cc-b8ef83115e1f' recording off (attempt 3)
2025-07-23 15:07:28,217 - WARNING - Failed to set camera '2fcd5a19-bd96-42c9-93cc-b8ef83115e1f' recording off (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/2fcd5a19-bd96-42c9-93cc-b8ef83115e1f
2025-07-23 15:07:28,217 - INFO - Camera 'load-test-camera-loadtest-agent-001-002' created with RTSP stream2
2025-07-23 15:07:28,217 - INFO - Camera 'load-test-camera-loadtest-agent-001-002' created with RTSP stream2
2025-07-23 15:07:28,718 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-003' (attempt 1)
2025-07-23 15:07:28,786 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-003' with ID: 0803b116-59f8-485b-a9eb-c1f08279bc39
2025-07-23 15:07:28,786 - INFO - Setting camera '0803b116-59f8-485b-a9eb-c1f08279bc39' recording off (attempt 1)
2025-07-23 15:07:28,821 - WARNING - Failed to set camera '0803b116-59f8-485b-a9eb-c1f08279bc39' recording off (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/0803b116-59f8-485b-a9eb-c1f08279bc39
2025-07-23 15:07:29,823 - INFO - Setting camera '0803b116-59f8-485b-a9eb-c1f08279bc39' recording off (attempt 2)
2025-07-23 15:07:29,875 - WARNING - Failed to set camera '0803b116-59f8-485b-a9eb-c1f08279bc39' recording off (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/0803b116-59f8-485b-a9eb-c1f08279bc39
2025-07-23 15:07:31,877 - INFO - Setting camera '0803b116-59f8-485b-a9eb-c1f08279bc39' recording off (attempt 3)
2025-07-23 15:07:31,910 - WARNING - Failed to set camera '0803b116-59f8-485b-a9eb-c1f08279bc39' recording off (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/0803b116-59f8-485b-a9eb-c1f08279bc39
2025-07-23 15:07:31,910 - INFO - Camera 'load-test-camera-loadtest-agent-001-003' created with RTSP stream3
2025-07-23 15:07:31,910 - INFO - Camera 'load-test-camera-loadtest-agent-001-003' created with RTSP stream3
2025-07-23 15:07:32,411 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-004' (attempt 1)
2025-07-23 15:07:32,482 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-004' with ID: 35c30c63-e4aa-47f3-9bd7-c5bfe21a6ef9
2025-07-23 15:07:32,482 - INFO - Setting camera '35c30c63-e4aa-47f3-9bd7-c5bfe21a6ef9' recording off (attempt 1)
2025-07-23 15:07:32,517 - WARNING - Failed to set camera '35c30c63-e4aa-47f3-9bd7-c5bfe21a6ef9' recording off (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/35c30c63-e4aa-47f3-9bd7-c5bfe21a6ef9
2025-07-23 15:07:33,518 - INFO - Setting camera '35c30c63-e4aa-47f3-9bd7-c5bfe21a6ef9' recording off (attempt 2)
2025-07-23 15:07:33,559 - WARNING - Failed to set camera '35c30c63-e4aa-47f3-9bd7-c5bfe21a6ef9' recording off (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/35c30c63-e4aa-47f3-9bd7-c5bfe21a6ef9
2025-07-23 15:07:35,561 - INFO - Setting camera '35c30c63-e4aa-47f3-9bd7-c5bfe21a6ef9' recording off (attempt 3)
2025-07-23 15:07:35,609 - WARNING - Failed to set camera '35c30c63-e4aa-47f3-9bd7-c5bfe21a6ef9' recording off (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/35c30c63-e4aa-47f3-9bd7-c5bfe21a6ef9
2025-07-23 15:07:35,609 - INFO - Camera 'load-test-camera-loadtest-agent-001-004' created with RTSP stream4
2025-07-23 15:07:35,610 - INFO - Camera 'load-test-camera-loadtest-agent-001-004' created with RTSP stream4
2025-07-23 15:07:36,110 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-005' (attempt 1)
2025-07-23 15:07:36,185 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-005' with ID: 793bb99c-73a9-46af-9340-f82bd6990a3e
2025-07-23 15:07:36,185 - INFO - Setting camera '793bb99c-73a9-46af-9340-f82bd6990a3e' recording off (attempt 1)
2025-07-23 15:07:36,226 - WARNING - Failed to set camera '793bb99c-73a9-46af-9340-f82bd6990a3e' recording off (attempt 1): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/793bb99c-73a9-46af-9340-f82bd6990a3e
2025-07-23 15:07:37,228 - INFO - Setting camera '793bb99c-73a9-46af-9340-f82bd6990a3e' recording off (attempt 2)
2025-07-23 15:07:37,274 - WARNING - Failed to set camera '793bb99c-73a9-46af-9340-f82bd6990a3e' recording off (attempt 2): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/793bb99c-73a9-46af-9340-f82bd6990a3e
2025-07-23 15:07:39,277 - INFO - Setting camera '793bb99c-73a9-46af-9340-f82bd6990a3e' recording off (attempt 3)
2025-07-23 15:07:39,323 - WARNING - Failed to set camera '793bb99c-73a9-46af-9340-f82bd6990a3e' recording off (attempt 3): 400 Client Error: Bad Request for url: https://api.volks-dev.knizsoft.com/api/v1/cameras/793bb99c-73a9-46af-9340-f82bd6990a3e
2025-07-23 15:07:39,323 - INFO - Camera 'load-test-camera-loadtest-agent-001-005' created with RTSP stream5
2025-07-23 15:07:39,323 - INFO - Camera 'load-test-camera-loadtest-agent-001-005' created with RTSP stream5
2025-07-23 15:07:39,824 - WARNING - Agent 'loadtest-agent-001' setup incomplete: 10/5 cameras created
2025-07-23 15:07:39,824 - INFO - Setup complete: 0/1 agents created successfully
2025-07-23 15:07:39,824 - WARNING - Failed agents: [1]
2025-07-23 15:07:39,824 - ERROR - Load test setup failed!
2025-07-23 15:10:55,598 - INFO - Starting load test setup: 1 agents with 5 cameras each
2025-07-23 15:10:55,599 - INFO - Attempting login (attempt 1)
2025-07-23 15:10:55,778 - INFO - Login successful! Token expires in 3600 seconds
2025-07-23 15:10:55,779 - INFO - Cleared existing apikeys.txt
2025-07-23 15:10:55,779 - INFO - Cleared existing agent_ids.txt
2025-07-23 15:10:55,779 - INFO - Setting up agent 1/1
2025-07-23 15:10:55,779 - INFO - Creating agent 'loadtest-agent-001' (attempt 1)
2025-07-23 15:10:55,890 - INFO - Successfully created agent 'loadtest-agent-001' with ID: bab9f66f-ebc4-4b06-944e-2cd13ed8f1f3
2025-07-23 15:10:55,891 - INFO - Saved API key to apikeys.txt
2025-07-23 15:10:55,892 - INFO - Saved agent ID to agent_ids.txt
2025-07-23 15:10:55,892 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-001' (attempt 1)
2025-07-23 15:10:55,965 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-001' with ID: e55e5dbb-8431-4c06-bc08-1110fa7802ca
2025-07-23 15:10:55,965 - INFO - Setting camera 'e55e5dbb-8431-4c06-bc08-1110fa7802ca' recording off (attempt 1)
2025-07-23 15:10:56,021 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-001-001' recording off
2025-07-23 15:10:56,021 - INFO - Camera 'load-test-camera-loadtest-agent-001-001' created with RTSP stream1
2025-07-23 15:10:56,021 - INFO - Camera 'load-test-camera-loadtest-agent-001-001' created with RTSP stream1
2025-07-23 15:10:56,522 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-002' (attempt 1)
2025-07-23 15:10:56,596 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-002' with ID: d498866d-2c3a-477a-a4ad-bc8e892d708a
2025-07-23 15:10:56,596 - INFO - Setting camera 'd498866d-2c3a-477a-a4ad-bc8e892d708a' recording off (attempt 1)
2025-07-23 15:10:56,650 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-001-002' recording off
2025-07-23 15:10:56,650 - INFO - Camera 'load-test-camera-loadtest-agent-001-002' created with RTSP stream2
2025-07-23 15:10:56,650 - INFO - Camera 'load-test-camera-loadtest-agent-001-002' created with RTSP stream2
2025-07-23 15:10:57,151 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-003' (attempt 1)
2025-07-23 15:10:57,216 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-003' with ID: 4ab44e27-cc47-4cd7-9cdf-d2a06b162a24
2025-07-23 15:10:57,216 - INFO - Setting camera '4ab44e27-cc47-4cd7-9cdf-d2a06b162a24' recording off (attempt 1)
2025-07-23 15:10:57,269 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-001-003' recording off
2025-07-23 15:10:57,269 - INFO - Camera 'load-test-camera-loadtest-agent-001-003' created with RTSP stream3
2025-07-23 15:10:57,269 - INFO - Camera 'load-test-camera-loadtest-agent-001-003' created with RTSP stream3
2025-07-23 15:10:57,770 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-004' (attempt 1)
2025-07-23 15:10:57,840 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-004' with ID: e1d46a63-3253-4e3c-9bd2-763b2364e677
2025-07-23 15:10:57,840 - INFO - Setting camera 'e1d46a63-3253-4e3c-9bd2-763b2364e677' recording off (attempt 1)
2025-07-23 15:10:57,888 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-001-004' recording off
2025-07-23 15:10:57,888 - INFO - Camera 'load-test-camera-loadtest-agent-001-004' created with RTSP stream4
2025-07-23 15:10:57,888 - INFO - Camera 'load-test-camera-loadtest-agent-001-004' created with RTSP stream4
2025-07-23 15:10:58,389 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-005' (attempt 1)
2025-07-23 15:10:58,452 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-005' with ID: 56a96b04-4335-4109-8821-a2892826b5bb
2025-07-23 15:10:58,452 - INFO - Setting camera '56a96b04-4335-4109-8821-a2892826b5bb' recording off (attempt 1)
2025-07-23 15:10:58,509 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-001-005' recording off
2025-07-23 15:10:58,509 - INFO - Camera 'load-test-camera-loadtest-agent-001-005' created with RTSP stream5
2025-07-23 15:10:58,509 - INFO - Camera 'load-test-camera-loadtest-agent-001-005' created with RTSP stream5
2025-07-23 15:10:59,010 - WARNING - Agent 'loadtest-agent-001' setup incomplete: 10/5 cameras created
2025-07-23 15:10:59,010 - INFO - Setup complete: 0/1 agents created successfully
2025-07-23 15:10:59,010 - WARNING - Failed agents: [1]
2025-07-23 15:10:59,010 - ERROR - Load test setup failed!
2025-07-23 15:11:52,962 - INFO - Starting load test setup: 1 agents with 5 cameras each
2025-07-23 15:11:52,962 - INFO - Attempting login (attempt 1)
2025-07-23 15:11:53,209 - INFO - Login successful! Token expires in 3600 seconds
2025-07-23 15:11:53,210 - INFO - Cleared existing apikeys.txt
2025-07-23 15:11:53,210 - INFO - Cleared existing agent_ids.txt
2025-07-23 15:11:53,210 - INFO - Setting up agent 1/1
2025-07-23 15:11:53,210 - INFO - Creating agent 'loadtest-agent-001' (attempt 1)
2025-07-23 15:11:53,335 - INFO - Successfully created agent 'loadtest-agent-001' with ID: ab2c6659-3bc4-46d5-a70f-33df57697447
2025-07-23 15:11:53,336 - INFO - Saved API key to apikeys.txt
2025-07-23 15:11:53,336 - INFO - Saved agent ID to agent_ids.txt
2025-07-23 15:11:53,336 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-001' (attempt 1)
2025-07-23 15:11:53,399 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-001' with ID: 492f4787-d58d-4128-bbd5-d2fcab5ef292
2025-07-23 15:11:53,399 - INFO - Setting camera '492f4787-d58d-4128-bbd5-d2fcab5ef292' recording off (attempt 1)
2025-07-23 15:11:53,453 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-001-001' recording off
2025-07-23 15:11:53,453 - INFO - Camera 'load-test-camera-loadtest-agent-001-001' created with RTSP stream1
2025-07-23 15:11:53,953 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-002' (attempt 1)
2025-07-23 15:11:54,013 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-002' with ID: 0eb07ad0-9335-408f-8117-0ca21fc5e139
2025-07-23 15:11:54,013 - INFO - Setting camera '0eb07ad0-9335-408f-8117-0ca21fc5e139' recording off (attempt 1)
2025-07-23 15:11:54,065 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-001-002' recording off
2025-07-23 15:11:54,065 - INFO - Camera 'load-test-camera-loadtest-agent-001-002' created with RTSP stream2
2025-07-23 15:11:54,566 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-003' (attempt 1)
2025-07-23 15:11:54,640 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-003' with ID: 19bc099d-548f-432d-8b91-bf174ca7de56
2025-07-23 15:11:54,640 - INFO - Setting camera '19bc099d-548f-432d-8b91-bf174ca7de56' recording off (attempt 1)
2025-07-23 15:11:54,692 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-001-003' recording off
2025-07-23 15:11:54,692 - INFO - Camera 'load-test-camera-loadtest-agent-001-003' created with RTSP stream3
2025-07-23 15:11:55,192 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-004' (attempt 1)
2025-07-23 15:11:55,262 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-004' with ID: 79ad8f3a-739a-402a-9edd-a6342b81dbe7
2025-07-23 15:11:55,262 - INFO - Setting camera '79ad8f3a-739a-402a-9edd-a6342b81dbe7' recording off (attempt 1)
2025-07-23 15:11:55,319 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-001-004' recording off
2025-07-23 15:11:55,319 - INFO - Camera 'load-test-camera-loadtest-agent-001-004' created with RTSP stream4
2025-07-23 15:11:55,820 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-005' (attempt 1)
2025-07-23 15:11:55,893 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-005' with ID: 26f0bf46-1c82-4c53-b24c-c0faacab429e
2025-07-23 15:11:55,893 - INFO - Setting camera '26f0bf46-1c82-4c53-b24c-c0faacab429e' recording off (attempt 1)
2025-07-23 15:11:55,945 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-001-005' recording off
2025-07-23 15:11:55,945 - INFO - Camera 'load-test-camera-loadtest-agent-001-005' created with RTSP stream5
2025-07-23 15:11:56,446 - INFO - Successfully setup agent 'loadtest-agent-001' with 5 cameras
2025-07-23 15:11:56,446 - INFO - Setup complete: 1/1 agents created successfully
2025-07-23 15:11:56,446 - INFO - All API keys saved to apikeys.txt
2025-07-23 15:11:56,446 - INFO - All agent IDs saved to agent_ids.txt
2025-07-23 15:11:56,446 - INFO - Load test setup completed successfully!
2025-07-23 15:12:13,449 - INFO - Starting load test setup: 10 agents with 5 cameras each
2025-07-23 15:12:13,449 - INFO - Attempting login (attempt 1)
2025-07-23 15:12:13,622 - INFO - Login successful! Token expires in 3600 seconds
2025-07-23 15:12:13,622 - INFO - Cleared existing apikeys.txt
2025-07-23 15:12:13,622 - INFO - Cleared existing agent_ids.txt
2025-07-23 15:12:13,622 - INFO - Setting up agent 1/10
2025-07-23 15:12:13,623 - INFO - Creating agent 'loadtest-agent-001' (attempt 1)
2025-07-23 15:12:13,725 - INFO - Successfully created agent 'loadtest-agent-001' with ID: 3aca8180-c791-4ffb-bb20-338801c7f32e
2025-07-23 15:12:13,726 - INFO - Saved API key to apikeys.txt
2025-07-23 15:12:13,727 - INFO - Saved agent ID to agent_ids.txt
2025-07-23 15:12:13,727 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-001' (attempt 1)
2025-07-23 15:12:13,796 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-001' with ID: c34ebeeb-015f-4a33-bebb-b4fe42a94f59
2025-07-23 15:12:13,796 - INFO - Setting camera 'c34ebeeb-015f-4a33-bebb-b4fe42a94f59' recording off (attempt 1)
2025-07-23 15:12:13,852 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-001-001' recording off
2025-07-23 15:12:13,853 - INFO - Camera 'load-test-camera-loadtest-agent-001-001' created with RTSP stream1
2025-07-23 15:12:14,353 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-002' (attempt 1)
2025-07-23 15:12:14,415 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-002' with ID: 49f2a1d2-18e6-48f9-9d88-e553a2be8433
2025-07-23 15:12:14,415 - INFO - Setting camera '49f2a1d2-18e6-48f9-9d88-e553a2be8433' recording off (attempt 1)
2025-07-23 15:12:14,463 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-001-002' recording off
2025-07-23 15:12:14,463 - INFO - Camera 'load-test-camera-loadtest-agent-001-002' created with RTSP stream2
2025-07-23 15:12:14,964 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-003' (attempt 1)
2025-07-23 15:12:15,027 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-003' with ID: 74ae8223-451c-4aed-91f8-32970a6381d3
2025-07-23 15:12:15,027 - INFO - Setting camera '74ae8223-451c-4aed-91f8-32970a6381d3' recording off (attempt 1)
2025-07-23 15:12:15,091 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-001-003' recording off
2025-07-23 15:12:15,091 - INFO - Camera 'load-test-camera-loadtest-agent-001-003' created with RTSP stream3
2025-07-23 15:12:15,592 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-004' (attempt 1)
2025-07-23 15:12:15,654 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-004' with ID: ff8b26a8-860f-4480-9ac1-9ee1d70f8f97
2025-07-23 15:12:15,654 - INFO - Setting camera 'ff8b26a8-860f-4480-9ac1-9ee1d70f8f97' recording off (attempt 1)
2025-07-23 15:12:15,713 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-001-004' recording off
2025-07-23 15:12:15,713 - INFO - Camera 'load-test-camera-loadtest-agent-001-004' created with RTSP stream4
2025-07-23 15:12:16,214 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-005' (attempt 1)
2025-07-23 15:12:16,274 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-005' with ID: b46b7b8d-caff-4a00-9c6e-248381cf39c5
2025-07-23 15:12:16,274 - INFO - Setting camera 'b46b7b8d-caff-4a00-9c6e-248381cf39c5' recording off (attempt 1)
2025-07-23 15:12:16,331 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-001-005' recording off
2025-07-23 15:12:16,332 - INFO - Camera 'load-test-camera-loadtest-agent-001-005' created with RTSP stream5
2025-07-23 15:12:16,832 - INFO - Successfully setup agent 'loadtest-agent-001' with 5 cameras
2025-07-23 15:12:17,333 - INFO - Setting up agent 2/10
2025-07-23 15:12:17,333 - INFO - Creating agent 'loadtest-agent-002' (attempt 1)
2025-07-23 15:12:17,388 - INFO - Successfully created agent 'loadtest-agent-002' with ID: fe8f0408-db7f-417d-bfd7-d1a5d2edbd25
2025-07-23 15:12:17,388 - INFO - Saved API key to apikeys.txt
2025-07-23 15:12:17,389 - INFO - Saved agent ID to agent_ids.txt
2025-07-23 15:12:17,389 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-001' (attempt 1)
2025-07-23 15:12:17,454 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-001' with ID: 370938c4-5820-45b5-b8c8-00a0ce90f436
2025-07-23 15:12:17,454 - INFO - Setting camera '370938c4-5820-45b5-b8c8-00a0ce90f436' recording off (attempt 1)
2025-07-23 15:12:17,504 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-002-001' recording off
2025-07-23 15:12:17,505 - INFO - Camera 'load-test-camera-loadtest-agent-002-001' created with RTSP stream1
2025-07-23 15:12:18,006 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-002' (attempt 1)
2025-07-23 15:12:18,071 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-002' with ID: 0acb1ae2-0cdf-4fac-8575-ca7d952d5310
2025-07-23 15:12:18,071 - INFO - Setting camera '0acb1ae2-0cdf-4fac-8575-ca7d952d5310' recording off (attempt 1)
2025-07-23 15:12:18,125 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-002-002' recording off
2025-07-23 15:12:18,125 - INFO - Camera 'load-test-camera-loadtest-agent-002-002' created with RTSP stream2
2025-07-23 15:12:18,626 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-003' (attempt 1)
2025-07-23 15:12:18,710 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-003' with ID: dbad78f8-3012-4772-8af1-afa8d12c2b28
2025-07-23 15:12:18,710 - INFO - Setting camera 'dbad78f8-3012-4772-8af1-afa8d12c2b28' recording off (attempt 1)
2025-07-23 15:12:18,765 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-002-003' recording off
2025-07-23 15:12:18,765 - INFO - Camera 'load-test-camera-loadtest-agent-002-003' created with RTSP stream3
2025-07-23 15:12:19,265 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-004' (attempt 1)
2025-07-23 15:12:19,330 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-004' with ID: 26c018e3-c4b0-4377-9acb-7b766ad1f65a
2025-07-23 15:12:19,330 - INFO - Setting camera '26c018e3-c4b0-4377-9acb-7b766ad1f65a' recording off (attempt 1)
2025-07-23 15:12:19,402 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-002-004' recording off
2025-07-23 15:12:19,402 - INFO - Camera 'load-test-camera-loadtest-agent-002-004' created with RTSP stream4
2025-07-23 15:12:19,903 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-005' (attempt 1)
2025-07-23 15:12:19,980 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-005' with ID: 7267a73d-f990-4bee-b3a6-989ac7811b00
2025-07-23 15:12:19,980 - INFO - Setting camera '7267a73d-f990-4bee-b3a6-989ac7811b00' recording off (attempt 1)
2025-07-23 15:12:20,046 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-002-005' recording off
2025-07-23 15:12:20,046 - INFO - Camera 'load-test-camera-loadtest-agent-002-005' created with RTSP stream5
2025-07-23 15:12:20,547 - INFO - Successfully setup agent 'loadtest-agent-002' with 5 cameras
2025-07-23 15:12:21,047 - INFO - Setting up agent 3/10
2025-07-23 15:12:21,048 - INFO - Creating agent 'loadtest-agent-003' (attempt 1)
2025-07-23 15:12:21,114 - INFO - Successfully created agent 'loadtest-agent-003' with ID: 8bc0401f-73e3-43d0-b469-01f070505d50
2025-07-23 15:12:21,114 - INFO - Saved API key to apikeys.txt
2025-07-23 15:12:21,114 - INFO - Saved agent ID to agent_ids.txt
2025-07-23 15:12:21,115 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-001' (attempt 1)
2025-07-23 15:12:21,172 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-001' with ID: a462aedd-65bd-4acd-b7e3-98761133a707
2025-07-23 15:12:21,173 - INFO - Setting camera 'a462aedd-65bd-4acd-b7e3-98761133a707' recording off (attempt 1)
2025-07-23 15:12:21,218 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-003-001' recording off
2025-07-23 15:12:21,219 - INFO - Camera 'load-test-camera-loadtest-agent-003-001' created with RTSP stream1
2025-07-23 15:12:21,719 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-002' (attempt 1)
2025-07-23 15:12:21,793 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-002' with ID: 8e6df022-534d-4c6d-90a7-ff573c07a362
2025-07-23 15:12:21,793 - INFO - Setting camera '8e6df022-534d-4c6d-90a7-ff573c07a362' recording off (attempt 1)
2025-07-23 15:12:21,844 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-003-002' recording off
2025-07-23 15:12:21,844 - INFO - Camera 'load-test-camera-loadtest-agent-003-002' created with RTSP stream2
2025-07-23 15:12:22,345 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-003' (attempt 1)
2025-07-23 15:12:22,402 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-003' with ID: 8b0cfc40-93ac-48e1-9ea2-ea7985e4e94a
2025-07-23 15:12:22,402 - INFO - Setting camera '8b0cfc40-93ac-48e1-9ea2-ea7985e4e94a' recording off (attempt 1)
2025-07-23 15:12:22,452 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-003-003' recording off
2025-07-23 15:12:22,452 - INFO - Camera 'load-test-camera-loadtest-agent-003-003' created with RTSP stream3
2025-07-23 15:12:22,953 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-004' (attempt 1)
2025-07-23 15:12:23,024 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-004' with ID: 2dc9962c-8ecf-4f8b-be39-1138bce11381
2025-07-23 15:12:23,024 - INFO - Setting camera '2dc9962c-8ecf-4f8b-be39-1138bce11381' recording off (attempt 1)
2025-07-23 15:12:23,077 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-003-004' recording off
2025-07-23 15:12:23,077 - INFO - Camera 'load-test-camera-loadtest-agent-003-004' created with RTSP stream4
2025-07-23 15:12:23,578 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-005' (attempt 1)
2025-07-23 15:12:23,664 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-005' with ID: 50e7a16b-6ee4-4bde-8174-f6dff2bf62d6
2025-07-23 15:12:23,664 - INFO - Setting camera '50e7a16b-6ee4-4bde-8174-f6dff2bf62d6' recording off (attempt 1)
2025-07-23 15:12:23,726 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-003-005' recording off
2025-07-23 15:12:23,726 - INFO - Camera 'load-test-camera-loadtest-agent-003-005' created with RTSP stream5
2025-07-23 15:12:24,227 - INFO - Successfully setup agent 'loadtest-agent-003' with 5 cameras
2025-07-23 15:12:24,727 - INFO - Setting up agent 4/10
2025-07-23 15:12:24,727 - INFO - Creating agent 'loadtest-agent-004' (attempt 1)
2025-07-23 15:12:24,791 - INFO - Successfully created agent 'loadtest-agent-004' with ID: 92b5bbdd-129a-43a8-9e5c-d0e7dd73fd9b
2025-07-23 15:12:24,791 - INFO - Saved API key to apikeys.txt
2025-07-23 15:12:24,791 - INFO - Saved agent ID to agent_ids.txt
2025-07-23 15:12:24,791 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-001' (attempt 1)
2025-07-23 15:12:24,851 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-001' with ID: db47a8a6-b70f-41cd-b367-d74ea998cd91
2025-07-23 15:12:24,851 - INFO - Setting camera 'db47a8a6-b70f-41cd-b367-d74ea998cd91' recording off (attempt 1)
2025-07-23 15:12:24,916 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-004-001' recording off
2025-07-23 15:12:24,916 - INFO - Camera 'load-test-camera-loadtest-agent-004-001' created with RTSP stream1
2025-07-23 15:12:25,417 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-002' (attempt 1)
2025-07-23 15:12:25,482 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-002' with ID: 1c226635-fa67-4c5c-b663-30782c7bbd99
2025-07-23 15:12:25,482 - INFO - Setting camera '1c226635-fa67-4c5c-b663-30782c7bbd99' recording off (attempt 1)
2025-07-23 15:12:25,539 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-004-002' recording off
2025-07-23 15:12:25,539 - INFO - Camera 'load-test-camera-loadtest-agent-004-002' created with RTSP stream2
2025-07-23 15:12:26,040 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-003' (attempt 1)
2025-07-23 15:12:26,107 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-003' with ID: ed82146d-17bc-4910-94f8-639481202fed
2025-07-23 15:12:26,107 - INFO - Setting camera 'ed82146d-17bc-4910-94f8-639481202fed' recording off (attempt 1)
2025-07-23 15:12:26,160 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-004-003' recording off
2025-07-23 15:12:26,160 - INFO - Camera 'load-test-camera-loadtest-agent-004-003' created with RTSP stream3
2025-07-23 15:12:26,661 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-004' (attempt 1)
2025-07-23 15:12:26,725 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-004' with ID: bbb3a3c2-d349-4cc7-886e-422d057eb949
2025-07-23 15:12:26,725 - INFO - Setting camera 'bbb3a3c2-d349-4cc7-886e-422d057eb949' recording off (attempt 1)
2025-07-23 15:12:26,774 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-004-004' recording off
2025-07-23 15:12:26,774 - INFO - Camera 'load-test-camera-loadtest-agent-004-004' created with RTSP stream4
2025-07-23 15:12:27,275 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-005' (attempt 1)
2025-07-23 15:12:27,342 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-005' with ID: e4c19cc4-26ed-4660-8a90-8c447f9ca3c9
2025-07-23 15:12:27,342 - INFO - Setting camera 'e4c19cc4-26ed-4660-8a90-8c447f9ca3c9' recording off (attempt 1)
2025-07-23 15:12:27,389 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-004-005' recording off
2025-07-23 15:12:27,390 - INFO - Camera 'load-test-camera-loadtest-agent-004-005' created with RTSP stream5
2025-07-23 15:12:27,890 - INFO - Successfully setup agent 'loadtest-agent-004' with 5 cameras
2025-07-23 15:12:28,391 - INFO - Setting up agent 5/10
2025-07-23 15:12:28,391 - INFO - Creating agent 'loadtest-agent-005' (attempt 1)
2025-07-23 15:12:28,454 - INFO - Successfully created agent 'loadtest-agent-005' with ID: 43be416d-3476-4781-890c-ab049e8861c8
2025-07-23 15:12:28,454 - INFO - Saved API key to apikeys.txt
2025-07-23 15:12:28,455 - INFO - Saved agent ID to agent_ids.txt
2025-07-23 15:12:28,455 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-001' (attempt 1)
2025-07-23 15:12:28,518 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-001' with ID: 2bd4ef5d-62eb-420c-a657-bb17044d6b5c
2025-07-23 15:12:28,518 - INFO - Setting camera '2bd4ef5d-62eb-420c-a657-bb17044d6b5c' recording off (attempt 1)
2025-07-23 15:12:28,575 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-005-001' recording off
2025-07-23 15:12:28,576 - INFO - Camera 'load-test-camera-loadtest-agent-005-001' created with RTSP stream1
2025-07-23 15:12:29,076 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-002' (attempt 1)
2025-07-23 15:12:29,142 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-002' with ID: b2e697f0-beba-48b6-abe2-eb7091727848
2025-07-23 15:12:29,142 - INFO - Setting camera 'b2e697f0-beba-48b6-abe2-eb7091727848' recording off (attempt 1)
2025-07-23 15:12:29,189 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-005-002' recording off
2025-07-23 15:12:29,189 - INFO - Camera 'load-test-camera-loadtest-agent-005-002' created with RTSP stream2
2025-07-23 15:12:29,690 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-003' (attempt 1)
2025-07-23 15:12:29,749 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-003' with ID: 5a037b6a-ccd8-4d21-a95c-022525d6b01b
2025-07-23 15:12:29,749 - INFO - Setting camera '5a037b6a-ccd8-4d21-a95c-022525d6b01b' recording off (attempt 1)
2025-07-23 15:12:29,799 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-005-003' recording off
2025-07-23 15:12:29,799 - INFO - Camera 'load-test-camera-loadtest-agent-005-003' created with RTSP stream3
2025-07-23 15:12:30,300 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-004' (attempt 1)
2025-07-23 15:12:30,365 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-004' with ID: 1c1a0ab9-53a4-4b15-961a-cc61f90d3dcb
2025-07-23 15:12:30,366 - INFO - Setting camera '1c1a0ab9-53a4-4b15-961a-cc61f90d3dcb' recording off (attempt 1)
2025-07-23 15:12:30,417 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-005-004' recording off
2025-07-23 15:12:30,417 - INFO - Camera 'load-test-camera-loadtest-agent-005-004' created with RTSP stream4
2025-07-23 15:12:30,918 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-005' (attempt 1)
2025-07-23 15:12:30,982 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-005' with ID: 9f87e194-766e-4eae-a05c-529bb0a5f1ae
2025-07-23 15:12:30,983 - INFO - Setting camera '9f87e194-766e-4eae-a05c-529bb0a5f1ae' recording off (attempt 1)
2025-07-23 15:12:31,041 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-005-005' recording off
2025-07-23 15:12:31,041 - INFO - Camera 'load-test-camera-loadtest-agent-005-005' created with RTSP stream5
2025-07-23 15:12:31,542 - INFO - Successfully setup agent 'loadtest-agent-005' with 5 cameras
2025-07-23 15:12:32,042 - INFO - Setting up agent 6/10
2025-07-23 15:12:32,043 - INFO - Creating agent 'loadtest-agent-006' (attempt 1)
2025-07-23 15:12:32,109 - INFO - Successfully created agent 'loadtest-agent-006' with ID: cd5f4480-2282-452d-8efe-c6e4d7f2c166
2025-07-23 15:12:32,109 - INFO - Saved API key to apikeys.txt
2025-07-23 15:12:32,109 - INFO - Saved agent ID to agent_ids.txt
2025-07-23 15:12:32,109 - INFO - Creating camera 'load-test-camera-loadtest-agent-006-001' (attempt 1)
2025-07-23 15:12:32,183 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-006-001' with ID: b6fe0d36-b751-4223-b0ac-a9473dd5f357
2025-07-23 15:12:32,184 - INFO - Setting camera 'b6fe0d36-b751-4223-b0ac-a9473dd5f357' recording off (attempt 1)
2025-07-23 15:12:32,235 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-006-001' recording off
2025-07-23 15:12:32,235 - INFO - Camera 'load-test-camera-loadtest-agent-006-001' created with RTSP stream1
2025-07-23 15:12:32,736 - INFO - Creating camera 'load-test-camera-loadtest-agent-006-002' (attempt 1)
2025-07-23 15:12:32,812 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-006-002' with ID: a924741d-27bc-4989-aa56-614e78a026bc
2025-07-23 15:12:32,812 - INFO - Setting camera 'a924741d-27bc-4989-aa56-614e78a026bc' recording off (attempt 1)
2025-07-23 15:12:32,875 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-006-002' recording off
2025-07-23 15:12:32,875 - INFO - Camera 'load-test-camera-loadtest-agent-006-002' created with RTSP stream2
2025-07-23 15:12:33,376 - INFO - Creating camera 'load-test-camera-loadtest-agent-006-003' (attempt 1)
2025-07-23 15:12:33,438 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-006-003' with ID: 7ffeb5a9-50de-4f49-85c6-4368c9ba5702
2025-07-23 15:12:33,438 - INFO - Setting camera '7ffeb5a9-50de-4f49-85c6-4368c9ba5702' recording off (attempt 1)
2025-07-23 15:12:33,485 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-006-003' recording off
2025-07-23 15:12:33,485 - INFO - Camera 'load-test-camera-loadtest-agent-006-003' created with RTSP stream3
2025-07-23 15:12:33,986 - INFO - Creating camera 'load-test-camera-loadtest-agent-006-004' (attempt 1)
2025-07-23 15:12:34,057 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-006-004' with ID: bc7cf68c-c3e3-4580-843f-f101ac6ed91e
2025-07-23 15:12:34,057 - INFO - Setting camera 'bc7cf68c-c3e3-4580-843f-f101ac6ed91e' recording off (attempt 1)
2025-07-23 15:12:34,112 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-006-004' recording off
2025-07-23 15:12:34,113 - INFO - Camera 'load-test-camera-loadtest-agent-006-004' created with RTSP stream4
2025-07-23 15:12:34,613 - INFO - Creating camera 'load-test-camera-loadtest-agent-006-005' (attempt 1)
2025-07-23 15:12:34,674 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-006-005' with ID: 75acb73e-c07d-4652-a4dc-8b06ee26c42e
2025-07-23 15:12:34,674 - INFO - Setting camera '75acb73e-c07d-4652-a4dc-8b06ee26c42e' recording off (attempt 1)
2025-07-23 15:12:34,728 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-006-005' recording off
2025-07-23 15:12:34,728 - INFO - Camera 'load-test-camera-loadtest-agent-006-005' created with RTSP stream5
2025-07-23 15:12:35,229 - INFO - Successfully setup agent 'loadtest-agent-006' with 5 cameras
2025-07-23 15:12:35,729 - INFO - Setting up agent 7/10
2025-07-23 15:12:35,730 - INFO - Creating agent 'loadtest-agent-007' (attempt 1)
2025-07-23 15:12:35,791 - INFO - Successfully created agent 'loadtest-agent-007' with ID: b2753514-cdcb-4263-8be3-d730d8d7bd4c
2025-07-23 15:12:35,791 - INFO - Saved API key to apikeys.txt
2025-07-23 15:12:35,791 - INFO - Saved agent ID to agent_ids.txt
2025-07-23 15:12:35,791 - INFO - Creating camera 'load-test-camera-loadtest-agent-007-001' (attempt 1)
2025-07-23 15:12:35,852 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-007-001' with ID: 4dc6d092-eec1-4655-9850-07e81ffc3486
2025-07-23 15:12:35,853 - INFO - Setting camera '4dc6d092-eec1-4655-9850-07e81ffc3486' recording off (attempt 1)
2025-07-23 15:12:35,905 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-007-001' recording off
2025-07-23 15:12:35,905 - INFO - Camera 'load-test-camera-loadtest-agent-007-001' created with RTSP stream1
2025-07-23 15:12:36,406 - INFO - Creating camera 'load-test-camera-loadtest-agent-007-002' (attempt 1)
2025-07-23 15:12:36,470 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-007-002' with ID: f9822eb5-bb58-4a83-89c2-30bb7131f3a7
2025-07-23 15:12:36,470 - INFO - Setting camera 'f9822eb5-bb58-4a83-89c2-30bb7131f3a7' recording off (attempt 1)
2025-07-23 15:12:36,522 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-007-002' recording off
2025-07-23 15:12:36,522 - INFO - Camera 'load-test-camera-loadtest-agent-007-002' created with RTSP stream2
2025-07-23 15:12:37,022 - INFO - Creating camera 'load-test-camera-loadtest-agent-007-003' (attempt 1)
2025-07-23 15:12:37,089 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-007-003' with ID: 4d3d2204-da64-4428-a260-0f4c51301b37
2025-07-23 15:12:37,089 - INFO - Setting camera '4d3d2204-da64-4428-a260-0f4c51301b37' recording off (attempt 1)
2025-07-23 15:12:37,139 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-007-003' recording off
2025-07-23 15:12:37,139 - INFO - Camera 'load-test-camera-loadtest-agent-007-003' created with RTSP stream3
2025-07-23 15:12:37,639 - INFO - Creating camera 'load-test-camera-loadtest-agent-007-004' (attempt 1)
2025-07-23 15:12:37,716 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-007-004' with ID: ecec5b7a-ad2a-4d31-be73-7ac76e6fbaff
2025-07-23 15:12:37,716 - INFO - Setting camera 'ecec5b7a-ad2a-4d31-be73-7ac76e6fbaff' recording off (attempt 1)
2025-07-23 15:12:37,775 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-007-004' recording off
2025-07-23 15:12:37,775 - INFO - Camera 'load-test-camera-loadtest-agent-007-004' created with RTSP stream4
2025-07-23 15:12:38,276 - INFO - Creating camera 'load-test-camera-loadtest-agent-007-005' (attempt 1)
2025-07-23 15:12:38,343 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-007-005' with ID: 60a49b57-659f-4c5c-b1bb-52d8e12e5665
2025-07-23 15:12:38,344 - INFO - Setting camera '60a49b57-659f-4c5c-b1bb-52d8e12e5665' recording off (attempt 1)
2025-07-23 15:12:38,391 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-007-005' recording off
2025-07-23 15:12:38,391 - INFO - Camera 'load-test-camera-loadtest-agent-007-005' created with RTSP stream5
2025-07-23 15:12:38,892 - INFO - Successfully setup agent 'loadtest-agent-007' with 5 cameras
2025-07-23 15:12:39,392 - INFO - Setting up agent 8/10
2025-07-23 15:12:39,393 - INFO - Creating agent 'loadtest-agent-008' (attempt 1)
2025-07-23 15:12:39,455 - INFO - Successfully created agent 'loadtest-agent-008' with ID: d41e779f-5101-4478-af5b-6d5a25d2747e
2025-07-23 15:12:39,455 - INFO - Saved API key to apikeys.txt
2025-07-23 15:12:39,456 - INFO - Saved agent ID to agent_ids.txt
2025-07-23 15:12:39,456 - INFO - Creating camera 'load-test-camera-loadtest-agent-008-001' (attempt 1)
2025-07-23 15:12:39,522 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-008-001' with ID: d54c9ebb-7fbd-43be-ba3d-f7560d75eb1b
2025-07-23 15:12:39,522 - INFO - Setting camera 'd54c9ebb-7fbd-43be-ba3d-f7560d75eb1b' recording off (attempt 1)
2025-07-23 15:12:39,576 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-008-001' recording off
2025-07-23 15:12:39,576 - INFO - Camera 'load-test-camera-loadtest-agent-008-001' created with RTSP stream1
2025-07-23 15:12:40,077 - INFO - Creating camera 'load-test-camera-loadtest-agent-008-002' (attempt 1)
2025-07-23 15:12:40,150 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-008-002' with ID: 620339c3-22b7-4bd1-9777-3d05abae6e38
2025-07-23 15:12:40,150 - INFO - Setting camera '620339c3-22b7-4bd1-9777-3d05abae6e38' recording off (attempt 1)
2025-07-23 15:12:40,208 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-008-002' recording off
2025-07-23 15:12:40,208 - INFO - Camera 'load-test-camera-loadtest-agent-008-002' created with RTSP stream2
2025-07-23 15:12:40,709 - INFO - Creating camera 'load-test-camera-loadtest-agent-008-003' (attempt 1)
2025-07-23 15:12:40,770 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-008-003' with ID: 6a59238c-fccc-49a4-a601-4e9b63d1062f
2025-07-23 15:12:40,770 - INFO - Setting camera '6a59238c-fccc-49a4-a601-4e9b63d1062f' recording off (attempt 1)
2025-07-23 15:12:40,823 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-008-003' recording off
2025-07-23 15:12:40,823 - INFO - Camera 'load-test-camera-loadtest-agent-008-003' created with RTSP stream3
2025-07-23 15:12:41,324 - INFO - Creating camera 'load-test-camera-loadtest-agent-008-004' (attempt 1)
2025-07-23 15:12:41,387 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-008-004' with ID: afb64a9c-84ad-48c6-8df9-7bf0b0536ad7
2025-07-23 15:12:41,387 - INFO - Setting camera 'afb64a9c-84ad-48c6-8df9-7bf0b0536ad7' recording off (attempt 1)
2025-07-23 15:12:41,435 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-008-004' recording off
2025-07-23 15:12:41,435 - INFO - Camera 'load-test-camera-loadtest-agent-008-004' created with RTSP stream4
2025-07-23 15:12:41,936 - INFO - Creating camera 'load-test-camera-loadtest-agent-008-005' (attempt 1)
2025-07-23 15:12:42,227 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-008-005' with ID: ade2a690-4518-4f6c-b13d-6233ae296ad9
2025-07-23 15:12:42,227 - INFO - Setting camera 'ade2a690-4518-4f6c-b13d-6233ae296ad9' recording off (attempt 1)
2025-07-23 15:12:42,285 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-008-005' recording off
2025-07-23 15:12:42,285 - INFO - Camera 'load-test-camera-loadtest-agent-008-005' created with RTSP stream5
2025-07-23 15:12:42,785 - INFO - Successfully setup agent 'loadtest-agent-008' with 5 cameras
2025-07-23 15:12:43,286 - INFO - Setting up agent 9/10
2025-07-23 15:12:43,286 - INFO - Creating agent 'loadtest-agent-009' (attempt 1)
2025-07-23 15:12:43,351 - INFO - Successfully created agent 'loadtest-agent-009' with ID: bae7299f-d7fd-4dc8-af60-5b66bfa0c1f7
2025-07-23 15:12:43,351 - INFO - Saved API key to apikeys.txt
2025-07-23 15:12:43,351 - INFO - Saved agent ID to agent_ids.txt
2025-07-23 15:12:43,351 - INFO - Creating camera 'load-test-camera-loadtest-agent-009-001' (attempt 1)
2025-07-23 15:12:43,415 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-009-001' with ID: 8be14c3d-5832-462c-8bbb-2cc6a0c7ea6f
2025-07-23 15:12:43,415 - INFO - Setting camera '8be14c3d-5832-462c-8bbb-2cc6a0c7ea6f' recording off (attempt 1)
2025-07-23 15:12:43,490 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-009-001' recording off
2025-07-23 15:12:43,491 - INFO - Camera 'load-test-camera-loadtest-agent-009-001' created with RTSP stream1
2025-07-23 15:12:43,991 - INFO - Creating camera 'load-test-camera-loadtest-agent-009-002' (attempt 1)
2025-07-23 15:12:44,051 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-009-002' with ID: 26866f7f-85be-4e53-85c9-776bb0785a35
2025-07-23 15:12:44,051 - INFO - Setting camera '26866f7f-85be-4e53-85c9-776bb0785a35' recording off (attempt 1)
2025-07-23 15:12:44,115 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-009-002' recording off
2025-07-23 15:12:44,115 - INFO - Camera 'load-test-camera-loadtest-agent-009-002' created with RTSP stream2
2025-07-23 15:12:44,616 - INFO - Creating camera 'load-test-camera-loadtest-agent-009-003' (attempt 1)
2025-07-23 15:12:44,687 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-009-003' with ID: 9de51f5b-fec4-4e42-b25e-e012ec9e38f0
2025-07-23 15:12:44,687 - INFO - Setting camera '9de51f5b-fec4-4e42-b25e-e012ec9e38f0' recording off (attempt 1)
2025-07-23 15:12:44,733 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-009-003' recording off
2025-07-23 15:12:44,733 - INFO - Camera 'load-test-camera-loadtest-agent-009-003' created with RTSP stream3
2025-07-23 15:12:45,234 - INFO - Creating camera 'load-test-camera-loadtest-agent-009-004' (attempt 1)
2025-07-23 15:12:45,308 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-009-004' with ID: 743296af-052a-4670-b5fe-e84168643949
2025-07-23 15:12:45,308 - INFO - Setting camera '743296af-052a-4670-b5fe-e84168643949' recording off (attempt 1)
2025-07-23 15:12:45,358 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-009-004' recording off
2025-07-23 15:12:45,358 - INFO - Camera 'load-test-camera-loadtest-agent-009-004' created with RTSP stream4
2025-07-23 15:12:45,859 - INFO - Creating camera 'load-test-camera-loadtest-agent-009-005' (attempt 1)
2025-07-23 15:12:45,924 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-009-005' with ID: ffa6bd5c-ba7b-4e58-b329-d1429b58df3f
2025-07-23 15:12:45,925 - INFO - Setting camera 'ffa6bd5c-ba7b-4e58-b329-d1429b58df3f' recording off (attempt 1)
2025-07-23 15:12:45,982 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-009-005' recording off
2025-07-23 15:12:45,982 - INFO - Camera 'load-test-camera-loadtest-agent-009-005' created with RTSP stream5
2025-07-23 15:12:46,483 - INFO - Successfully setup agent 'loadtest-agent-009' with 5 cameras
2025-07-23 15:12:46,983 - INFO - Setting up agent 10/10
2025-07-23 15:12:46,984 - INFO - Creating agent 'loadtest-agent-010' (attempt 1)
2025-07-23 15:12:47,055 - INFO - Successfully created agent 'loadtest-agent-010' with ID: 8cd98fc7-e802-4935-8bfc-d5ff2dae0f77
2025-07-23 15:12:47,055 - INFO - Saved API key to apikeys.txt
2025-07-23 15:12:47,055 - INFO - Saved agent ID to agent_ids.txt
2025-07-23 15:12:47,055 - INFO - Creating camera 'load-test-camera-loadtest-agent-010-001' (attempt 1)
2025-07-23 15:12:47,129 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-010-001' with ID: 19ca344b-9238-449b-88e5-c2ebee601b0e
2025-07-23 15:12:47,129 - INFO - Setting camera '19ca344b-9238-449b-88e5-c2ebee601b0e' recording off (attempt 1)
2025-07-23 15:12:47,186 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-010-001' recording off
2025-07-23 15:12:47,186 - INFO - Camera 'load-test-camera-loadtest-agent-010-001' created with RTSP stream1
2025-07-23 15:12:47,687 - INFO - Creating camera 'load-test-camera-loadtest-agent-010-002' (attempt 1)
2025-07-23 15:12:47,765 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-010-002' with ID: 8a770272-5b09-4e0b-8989-6e71cb73cb65
2025-07-23 15:12:47,765 - INFO - Setting camera '8a770272-5b09-4e0b-8989-6e71cb73cb65' recording off (attempt 1)
2025-07-23 15:12:47,815 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-010-002' recording off
2025-07-23 15:12:47,815 - INFO - Camera 'load-test-camera-loadtest-agent-010-002' created with RTSP stream2
2025-07-23 15:12:48,315 - INFO - Creating camera 'load-test-camera-loadtest-agent-010-003' (attempt 1)
2025-07-23 15:12:48,380 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-010-003' with ID: 18b89e9b-293d-4c76-8db7-b10bdfb62018
2025-07-23 15:12:48,380 - INFO - Setting camera '18b89e9b-293d-4c76-8db7-b10bdfb62018' recording off (attempt 1)
2025-07-23 15:12:48,442 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-010-003' recording off
2025-07-23 15:12:48,443 - INFO - Camera 'load-test-camera-loadtest-agent-010-003' created with RTSP stream3
2025-07-23 15:12:48,943 - INFO - Creating camera 'load-test-camera-loadtest-agent-010-004' (attempt 1)
2025-07-23 15:12:49,006 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-010-004' with ID: eac0be98-81f0-44e9-801e-62303b7e513e
2025-07-23 15:12:49,006 - INFO - Setting camera 'eac0be98-81f0-44e9-801e-62303b7e513e' recording off (attempt 1)
2025-07-23 15:12:49,058 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-010-004' recording off
2025-07-23 15:12:49,058 - INFO - Camera 'load-test-camera-loadtest-agent-010-004' created with RTSP stream4
2025-07-23 15:12:49,559 - INFO - Creating camera 'load-test-camera-loadtest-agent-010-005' (attempt 1)
2025-07-23 15:12:49,639 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-010-005' with ID: 346e4d75-4a94-43b9-ad17-13fc7bf26f3d
2025-07-23 15:12:49,639 - INFO - Setting camera '346e4d75-4a94-43b9-ad17-13fc7bf26f3d' recording off (attempt 1)
2025-07-23 15:12:49,702 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-010-005' recording off
2025-07-23 15:12:49,702 - INFO - Camera 'load-test-camera-loadtest-agent-010-005' created with RTSP stream5
2025-07-23 15:12:50,203 - INFO - Successfully setup agent 'loadtest-agent-010' with 5 cameras
2025-07-23 15:12:50,203 - INFO - Setup complete: 10/10 agents created successfully
2025-07-23 15:12:50,203 - INFO - All API keys saved to apikeys.txt
2025-07-23 15:12:50,203 - INFO - All agent IDs saved to agent_ids.txt
2025-07-23 15:12:50,203 - INFO - Load test setup completed successfully!
2025-07-24 09:33:03,559 - INFO - Starting load test setup: 10 agents with 5 cameras each
2025-07-24 09:33:03,560 - INFO - Attempting login (attempt 1)
2025-07-24 09:33:03,766 - INFO - Login successful! Token expires in 3600 seconds
2025-07-24 09:33:03,766 - INFO - Cleared existing apikeys.txt
2025-07-24 09:33:03,766 - INFO - Cleared existing agent_ids.txt
2025-07-24 09:33:03,766 - INFO - Setting up agent 1/10
2025-07-24 09:33:03,766 - INFO - Creating agent 'loadtest-agent-001' (attempt 1)
2025-07-24 09:33:03,872 - INFO - Successfully created agent 'loadtest-agent-001' with ID: e928087e-60e6-4de6-9944-30dd0079e3af
2025-07-24 09:33:03,873 - INFO - Saved API key to apikeys.txt
2025-07-24 09:33:03,874 - INFO - Saved agent ID to agent_ids.txt
2025-07-24 09:33:03,874 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-001' (attempt 1)
2025-07-24 09:33:03,942 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-001' with ID: ec229925-107e-49d6-ad60-16694c7708d4
2025-07-24 09:33:03,942 - INFO - Setting camera 'ec229925-107e-49d6-ad60-16694c7708d4' recording off (attempt 1)
2025-07-24 09:33:03,999 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-001-001' recording off
2025-07-24 09:33:04,000 - INFO - Camera 'load-test-camera-loadtest-agent-001-001' created with RTSP stream1
2025-07-24 09:33:04,500 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-002' (attempt 1)
2025-07-24 09:33:04,574 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-002' with ID: b247bf28-1435-4c4d-8cfd-0c18a556435f
2025-07-24 09:33:04,574 - INFO - Setting camera 'b247bf28-1435-4c4d-8cfd-0c18a556435f' recording off (attempt 1)
2025-07-24 09:33:04,635 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-001-002' recording off
2025-07-24 09:33:04,635 - INFO - Camera 'load-test-camera-loadtest-agent-001-002' created with RTSP stream2
2025-07-24 09:33:05,136 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-003' (attempt 1)
2025-07-24 09:33:05,213 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-003' with ID: 14ca8a14-44fc-4055-95d9-a8b5b78974ea
2025-07-24 09:33:05,214 - INFO - Setting camera '14ca8a14-44fc-4055-95d9-a8b5b78974ea' recording off (attempt 1)
2025-07-24 09:33:05,292 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-001-003' recording off
2025-07-24 09:33:05,292 - INFO - Camera 'load-test-camera-loadtest-agent-001-003' created with RTSP stream3
2025-07-24 09:33:05,792 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-004' (attempt 1)
2025-07-24 09:33:05,847 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-004' with ID: a41ee954-5213-4923-9652-a869409b91f9
2025-07-24 09:33:05,847 - INFO - Setting camera 'a41ee954-5213-4923-9652-a869409b91f9' recording off (attempt 1)
2025-07-24 09:33:05,902 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-001-004' recording off
2025-07-24 09:33:05,902 - INFO - Camera 'load-test-camera-loadtest-agent-001-004' created with RTSP stream4
2025-07-24 09:33:06,403 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-005' (attempt 1)
2025-07-24 09:33:06,473 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-005' with ID: 906a7a7e-0a41-4af2-8043-6cba6534db35
2025-07-24 09:33:06,473 - INFO - Setting camera '906a7a7e-0a41-4af2-8043-6cba6534db35' recording off (attempt 1)
2025-07-24 09:33:06,521 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-001-005' recording off
2025-07-24 09:33:06,522 - INFO - Camera 'load-test-camera-loadtest-agent-001-005' created with RTSP stream5
2025-07-24 09:33:07,022 - INFO - Successfully setup agent 'loadtest-agent-001' with 5 cameras
2025-07-24 09:33:07,523 - INFO - Setting up agent 2/10
2025-07-24 09:33:07,523 - INFO - Creating agent 'loadtest-agent-002' (attempt 1)
2025-07-24 09:33:07,586 - INFO - Successfully created agent 'loadtest-agent-002' with ID: f4967553-7ab1-4d35-8d1f-59e11de66136
2025-07-24 09:33:07,586 - INFO - Saved API key to apikeys.txt
2025-07-24 09:33:07,586 - INFO - Saved agent ID to agent_ids.txt
2025-07-24 09:33:07,586 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-001' (attempt 1)
2025-07-24 09:33:07,662 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-001' with ID: b953ca78-2456-40a6-828c-929c11497681
2025-07-24 09:33:07,662 - INFO - Setting camera 'b953ca78-2456-40a6-828c-929c11497681' recording off (attempt 1)
2025-07-24 09:33:07,718 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-002-001' recording off
2025-07-24 09:33:07,718 - INFO - Camera 'load-test-camera-loadtest-agent-002-001' created with RTSP stream1
2025-07-24 09:33:08,218 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-002' (attempt 1)
2025-07-24 09:33:08,282 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-002' with ID: 316935ba-2896-414c-a694-f2bd2632f842
2025-07-24 09:33:08,282 - INFO - Setting camera '316935ba-2896-414c-a694-f2bd2632f842' recording off (attempt 1)
2025-07-24 09:33:08,395 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-002-002' recording off
2025-07-24 09:33:08,395 - INFO - Camera 'load-test-camera-loadtest-agent-002-002' created with RTSP stream2
2025-07-24 09:33:08,896 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-003' (attempt 1)
2025-07-24 09:33:08,969 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-003' with ID: eaea8654-59ca-4941-8a14-7d235dd443eb
2025-07-24 09:33:08,969 - INFO - Setting camera 'eaea8654-59ca-4941-8a14-7d235dd443eb' recording off (attempt 1)
2025-07-24 09:33:09,023 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-002-003' recording off
2025-07-24 09:33:09,023 - INFO - Camera 'load-test-camera-loadtest-agent-002-003' created with RTSP stream3
2025-07-24 09:33:09,523 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-004' (attempt 1)
2025-07-24 09:33:09,592 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-004' with ID: 4374d236-d93c-427b-b09b-70d7c695ac4c
2025-07-24 09:33:09,592 - INFO - Setting camera '4374d236-d93c-427b-b09b-70d7c695ac4c' recording off (attempt 1)
2025-07-24 09:33:09,672 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-002-004' recording off
2025-07-24 09:33:09,672 - INFO - Camera 'load-test-camera-loadtest-agent-002-004' created with RTSP stream4
2025-07-24 09:33:10,173 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-005' (attempt 1)
2025-07-24 09:33:10,242 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-005' with ID: bcb7f3a8-32a6-41f8-beeb-80b46a1d9479
2025-07-24 09:33:10,242 - INFO - Setting camera 'bcb7f3a8-32a6-41f8-beeb-80b46a1d9479' recording off (attempt 1)
2025-07-24 09:33:10,296 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-002-005' recording off
2025-07-24 09:33:10,296 - INFO - Camera 'load-test-camera-loadtest-agent-002-005' created with RTSP stream5
2025-07-24 09:33:10,796 - INFO - Successfully setup agent 'loadtest-agent-002' with 5 cameras
2025-07-24 09:33:11,297 - INFO - Setting up agent 3/10
2025-07-24 09:33:11,297 - INFO - Creating agent 'loadtest-agent-003' (attempt 1)
2025-07-24 09:33:11,372 - INFO - Successfully created agent 'loadtest-agent-003' with ID: aca35ae4-db5b-4586-ab64-a0a3386e89f5
2025-07-24 09:33:11,372 - INFO - Saved API key to apikeys.txt
2025-07-24 09:33:11,372 - INFO - Saved agent ID to agent_ids.txt
2025-07-24 09:33:11,372 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-001' (attempt 1)
2025-07-24 09:33:11,435 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-001' with ID: ebc9c6bb-c387-48c4-85fd-e8c66469e9fd
2025-07-24 09:33:11,436 - INFO - Setting camera 'ebc9c6bb-c387-48c4-85fd-e8c66469e9fd' recording off (attempt 1)
2025-07-24 09:33:11,492 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-003-001' recording off
2025-07-24 09:33:11,492 - INFO - Camera 'load-test-camera-loadtest-agent-003-001' created with RTSP stream1
2025-07-24 09:33:11,992 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-002' (attempt 1)
2025-07-24 09:33:12,079 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-002' with ID: b8923821-ab12-4dc6-ac29-d79114dbb219
2025-07-24 09:33:12,079 - INFO - Setting camera 'b8923821-ab12-4dc6-ac29-d79114dbb219' recording off (attempt 1)
2025-07-24 09:33:12,131 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-003-002' recording off
2025-07-24 09:33:12,131 - INFO - Camera 'load-test-camera-loadtest-agent-003-002' created with RTSP stream2
2025-07-24 09:33:12,632 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-003' (attempt 1)
2025-07-24 09:33:12,697 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-003' with ID: c4730ae1-c771-42e6-9beb-536c925ca2ce
2025-07-24 09:33:12,697 - INFO - Setting camera 'c4730ae1-c771-42e6-9beb-536c925ca2ce' recording off (attempt 1)
2025-07-24 09:33:12,751 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-003-003' recording off
2025-07-24 09:33:12,751 - INFO - Camera 'load-test-camera-loadtest-agent-003-003' created with RTSP stream3
2025-07-24 09:33:13,252 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-004' (attempt 1)
2025-07-24 09:33:13,314 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-004' with ID: de2ded95-2256-48f0-b2ad-5d92b399986f
2025-07-24 09:33:13,314 - INFO - Setting camera 'de2ded95-2256-48f0-b2ad-5d92b399986f' recording off (attempt 1)
2025-07-24 09:33:13,373 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-003-004' recording off
2025-07-24 09:33:13,373 - INFO - Camera 'load-test-camera-loadtest-agent-003-004' created with RTSP stream4
2025-07-24 09:33:13,874 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-005' (attempt 1)
2025-07-24 09:33:13,938 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-005' with ID: 3c28e9e5-1e4d-4063-909e-aa3184054515
2025-07-24 09:33:13,938 - INFO - Setting camera '3c28e9e5-1e4d-4063-909e-aa3184054515' recording off (attempt 1)
2025-07-24 09:33:13,994 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-003-005' recording off
2025-07-24 09:33:13,995 - INFO - Camera 'load-test-camera-loadtest-agent-003-005' created with RTSP stream5
2025-07-24 09:33:14,495 - INFO - Successfully setup agent 'loadtest-agent-003' with 5 cameras
2025-07-24 09:33:14,996 - INFO - Setting up agent 4/10
2025-07-24 09:33:14,996 - INFO - Creating agent 'loadtest-agent-004' (attempt 1)
2025-07-24 09:33:15,060 - INFO - Successfully created agent 'loadtest-agent-004' with ID: afc02353-b537-45ab-ad66-000df8842eec
2025-07-24 09:33:15,061 - INFO - Saved API key to apikeys.txt
2025-07-24 09:33:15,061 - INFO - Saved agent ID to agent_ids.txt
2025-07-24 09:33:15,061 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-001' (attempt 1)
2025-07-24 09:33:15,128 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-001' with ID: 8166ae96-72bd-4f6d-9f01-0b8ea331409d
2025-07-24 09:33:15,128 - INFO - Setting camera '8166ae96-72bd-4f6d-9f01-0b8ea331409d' recording off (attempt 1)
2025-07-24 09:33:15,179 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-004-001' recording off
2025-07-24 09:33:15,179 - INFO - Camera 'load-test-camera-loadtest-agent-004-001' created with RTSP stream1
2025-07-24 09:33:15,680 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-002' (attempt 1)
2025-07-24 09:33:15,743 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-002' with ID: 4d2fa238-068a-4f37-a6da-374bc59756cf
2025-07-24 09:33:15,743 - INFO - Setting camera '4d2fa238-068a-4f37-a6da-374bc59756cf' recording off (attempt 1)
2025-07-24 09:33:15,797 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-004-002' recording off
2025-07-24 09:33:15,797 - INFO - Camera 'load-test-camera-loadtest-agent-004-002' created with RTSP stream2
2025-07-24 09:33:16,297 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-003' (attempt 1)
2025-07-24 09:33:16,368 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-003' with ID: 5718946c-9f0a-4263-8ccb-6d766f2210e7
2025-07-24 09:33:16,368 - INFO - Setting camera '5718946c-9f0a-4263-8ccb-6d766f2210e7' recording off (attempt 1)
2025-07-24 09:33:16,418 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-004-003' recording off
2025-07-24 09:33:16,418 - INFO - Camera 'load-test-camera-loadtest-agent-004-003' created with RTSP stream3
2025-07-24 09:33:16,919 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-004' (attempt 1)
2025-07-24 09:33:16,979 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-004' with ID: 33b61283-b15b-44d6-af83-4c5e0f7a08d1
2025-07-24 09:33:16,979 - INFO - Setting camera '33b61283-b15b-44d6-af83-4c5e0f7a08d1' recording off (attempt 1)
2025-07-24 09:33:17,036 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-004-004' recording off
2025-07-24 09:33:17,036 - INFO - Camera 'load-test-camera-loadtest-agent-004-004' created with RTSP stream4
2025-07-24 09:33:17,536 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-005' (attempt 1)
2025-07-24 09:33:17,600 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-005' with ID: 24d6217f-caf8-4a84-a7ac-ee338072273a
2025-07-24 09:33:17,600 - INFO - Setting camera '24d6217f-caf8-4a84-a7ac-ee338072273a' recording off (attempt 1)
2025-07-24 09:33:17,655 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-004-005' recording off
2025-07-24 09:33:17,655 - INFO - Camera 'load-test-camera-loadtest-agent-004-005' created with RTSP stream5
2025-07-24 09:33:18,156 - INFO - Successfully setup agent 'loadtest-agent-004' with 5 cameras
2025-07-24 09:33:18,656 - INFO - Setting up agent 5/10
2025-07-24 09:33:18,656 - INFO - Creating agent 'loadtest-agent-005' (attempt 1)
2025-07-24 09:33:18,726 - INFO - Successfully created agent 'loadtest-agent-005' with ID: bac2b5a1-773d-47b4-8b6a-b71cf7e25b66
2025-07-24 09:33:18,727 - INFO - Saved API key to apikeys.txt
2025-07-24 09:33:18,727 - INFO - Saved agent ID to agent_ids.txt
2025-07-24 09:33:18,727 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-001' (attempt 1)
2025-07-24 09:33:18,794 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-001' with ID: 3e9b86c8-2e24-43ac-99e4-e1e6b7401222
2025-07-24 09:33:18,794 - INFO - Setting camera '3e9b86c8-2e24-43ac-99e4-e1e6b7401222' recording off (attempt 1)
2025-07-24 09:33:18,850 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-005-001' recording off
2025-07-24 09:33:18,851 - INFO - Camera 'load-test-camera-loadtest-agent-005-001' created with RTSP stream1
2025-07-24 09:33:19,351 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-002' (attempt 1)
2025-07-24 09:33:19,423 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-002' with ID: ebd13e54-52d0-45b6-a309-b1eea230169d
2025-07-24 09:33:19,423 - INFO - Setting camera 'ebd13e54-52d0-45b6-a309-b1eea230169d' recording off (attempt 1)
2025-07-24 09:33:19,472 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-005-002' recording off
2025-07-24 09:33:19,472 - INFO - Camera 'load-test-camera-loadtest-agent-005-002' created with RTSP stream2
2025-07-24 09:33:19,973 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-003' (attempt 1)
2025-07-24 09:33:20,047 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-003' with ID: 71197212-991e-481a-a67f-c0f3c72fd8d7
2025-07-24 09:33:20,047 - INFO - Setting camera '71197212-991e-481a-a67f-c0f3c72fd8d7' recording off (attempt 1)
2025-07-24 09:33:20,099 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-005-003' recording off
2025-07-24 09:33:20,099 - INFO - Camera 'load-test-camera-loadtest-agent-005-003' created with RTSP stream3
2025-07-24 09:33:20,600 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-004' (attempt 1)
2025-07-24 09:33:20,668 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-004' with ID: 1b8eb7ae-3612-4aaa-a8b4-0cf271a298da
2025-07-24 09:33:20,668 - INFO - Setting camera '1b8eb7ae-3612-4aaa-a8b4-0cf271a298da' recording off (attempt 1)
2025-07-24 09:33:20,721 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-005-004' recording off
2025-07-24 09:33:20,721 - INFO - Camera 'load-test-camera-loadtest-agent-005-004' created with RTSP stream4
2025-07-24 09:33:21,221 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-005' (attempt 1)
2025-07-24 09:33:21,292 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-005' with ID: b96d9249-ebc0-42b6-bb74-db114d2d89c0
2025-07-24 09:33:21,292 - INFO - Setting camera 'b96d9249-ebc0-42b6-bb74-db114d2d89c0' recording off (attempt 1)
2025-07-24 09:33:21,341 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-005-005' recording off
2025-07-24 09:33:21,342 - INFO - Camera 'load-test-camera-loadtest-agent-005-005' created with RTSP stream5
2025-07-24 09:33:21,842 - INFO - Successfully setup agent 'loadtest-agent-005' with 5 cameras
2025-07-24 09:33:22,343 - INFO - Setting up agent 6/10
2025-07-24 09:33:22,343 - INFO - Creating agent 'loadtest-agent-006' (attempt 1)
2025-07-24 09:33:22,403 - INFO - Successfully created agent 'loadtest-agent-006' with ID: 485fd47f-e861-43e6-9afa-0d320a213314
2025-07-24 09:33:22,404 - INFO - Saved API key to apikeys.txt
2025-07-24 09:33:22,404 - INFO - Saved agent ID to agent_ids.txt
2025-07-24 09:33:22,404 - INFO - Creating camera 'load-test-camera-loadtest-agent-006-001' (attempt 1)
2025-07-24 09:33:22,477 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-006-001' with ID: 397b43bb-9c60-4b96-8f9f-e72f027392f9
2025-07-24 09:33:22,478 - INFO - Setting camera '397b43bb-9c60-4b96-8f9f-e72f027392f9' recording off (attempt 1)
2025-07-24 09:33:22,534 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-006-001' recording off
2025-07-24 09:33:22,534 - INFO - Camera 'load-test-camera-loadtest-agent-006-001' created with RTSP stream1
2025-07-24 09:33:23,035 - INFO - Creating camera 'load-test-camera-loadtest-agent-006-002' (attempt 1)
2025-07-24 09:33:23,100 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-006-002' with ID: 9915e68f-36c8-42ae-9d3a-f46f20082491
2025-07-24 09:33:23,100 - INFO - Setting camera '9915e68f-36c8-42ae-9d3a-f46f20082491' recording off (attempt 1)
2025-07-24 09:33:23,144 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-006-002' recording off
2025-07-24 09:33:23,144 - INFO - Camera 'load-test-camera-loadtest-agent-006-002' created with RTSP stream2
2025-07-24 09:33:23,644 - INFO - Creating camera 'load-test-camera-loadtest-agent-006-003' (attempt 1)
2025-07-24 09:33:23,721 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-006-003' with ID: b0dc9444-f755-40aa-baf0-a6523e402a78
2025-07-24 09:33:23,722 - INFO - Setting camera 'b0dc9444-f755-40aa-baf0-a6523e402a78' recording off (attempt 1)
2025-07-24 09:33:23,778 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-006-003' recording off
2025-07-24 09:33:23,778 - INFO - Camera 'load-test-camera-loadtest-agent-006-003' created with RTSP stream3
2025-07-24 09:33:24,279 - INFO - Creating camera 'load-test-camera-loadtest-agent-006-004' (attempt 1)
2025-07-24 09:33:24,349 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-006-004' with ID: 17fb4c26-129c-471a-acf3-96d8fa9e971d
2025-07-24 09:33:24,349 - INFO - Setting camera '17fb4c26-129c-471a-acf3-96d8fa9e971d' recording off (attempt 1)
2025-07-24 09:33:24,401 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-006-004' recording off
2025-07-24 09:33:24,401 - INFO - Camera 'load-test-camera-loadtest-agent-006-004' created with RTSP stream4
2025-07-24 09:33:24,901 - INFO - Creating camera 'load-test-camera-loadtest-agent-006-005' (attempt 1)
2025-07-24 09:33:24,978 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-006-005' with ID: 2fce2640-da2c-4fb4-a7f4-45e523bbea1a
2025-07-24 09:33:24,978 - INFO - Setting camera '2fce2640-da2c-4fb4-a7f4-45e523bbea1a' recording off (attempt 1)
2025-07-24 09:33:25,030 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-006-005' recording off
2025-07-24 09:33:25,030 - INFO - Camera 'load-test-camera-loadtest-agent-006-005' created with RTSP stream5
2025-07-24 09:33:25,531 - INFO - Successfully setup agent 'loadtest-agent-006' with 5 cameras
2025-07-24 09:33:26,032 - INFO - Setting up agent 7/10
2025-07-24 09:33:26,032 - INFO - Creating agent 'loadtest-agent-007' (attempt 1)
2025-07-24 09:33:26,096 - INFO - Successfully created agent 'loadtest-agent-007' with ID: ce34552a-a0f0-45bc-a322-52377c724764
2025-07-24 09:33:26,096 - INFO - Saved API key to apikeys.txt
2025-07-24 09:33:26,096 - INFO - Saved agent ID to agent_ids.txt
2025-07-24 09:33:26,096 - INFO - Creating camera 'load-test-camera-loadtest-agent-007-001' (attempt 1)
2025-07-24 09:33:26,157 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-007-001' with ID: bf168669-80cd-40bb-bced-99a52c8fa7d9
2025-07-24 09:33:26,157 - INFO - Setting camera 'bf168669-80cd-40bb-bced-99a52c8fa7d9' recording off (attempt 1)
2025-07-24 09:33:26,206 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-007-001' recording off
2025-07-24 09:33:26,207 - INFO - Camera 'load-test-camera-loadtest-agent-007-001' created with RTSP stream1
2025-07-24 09:33:26,707 - INFO - Creating camera 'load-test-camera-loadtest-agent-007-002' (attempt 1)
2025-07-24 09:33:26,777 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-007-002' with ID: bba1cbc5-302c-468b-be78-a2cb879aa1ec
2025-07-24 09:33:26,777 - INFO - Setting camera 'bba1cbc5-302c-468b-be78-a2cb879aa1ec' recording off (attempt 1)
2025-07-24 09:33:26,823 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-007-002' recording off
2025-07-24 09:33:26,824 - INFO - Camera 'load-test-camera-loadtest-agent-007-002' created with RTSP stream2
2025-07-24 09:33:27,324 - INFO - Creating camera 'load-test-camera-loadtest-agent-007-003' (attempt 1)
2025-07-24 09:33:27,431 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-007-003' with ID: 90a714fd-8a5e-4fed-8852-edab4554f4f9
2025-07-24 09:33:27,431 - INFO - Setting camera '90a714fd-8a5e-4fed-8852-edab4554f4f9' recording off (attempt 1)
2025-07-24 09:33:27,502 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-007-003' recording off
2025-07-24 09:33:27,502 - INFO - Camera 'load-test-camera-loadtest-agent-007-003' created with RTSP stream3
2025-07-24 09:33:28,003 - INFO - Creating camera 'load-test-camera-loadtest-agent-007-004' (attempt 1)
2025-07-24 09:33:28,077 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-007-004' with ID: bc950382-a26a-49af-9106-28089bb986bd
2025-07-24 09:33:28,077 - INFO - Setting camera 'bc950382-a26a-49af-9106-28089bb986bd' recording off (attempt 1)
2025-07-24 09:33:28,128 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-007-004' recording off
2025-07-24 09:33:28,128 - INFO - Camera 'load-test-camera-loadtest-agent-007-004' created with RTSP stream4
2025-07-24 09:33:28,629 - INFO - Creating camera 'load-test-camera-loadtest-agent-007-005' (attempt 1)
2025-07-24 09:33:28,707 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-007-005' with ID: d887e20f-e6a8-4270-8001-4b567482dbbb
2025-07-24 09:33:28,707 - INFO - Setting camera 'd887e20f-e6a8-4270-8001-4b567482dbbb' recording off (attempt 1)
2025-07-24 09:33:28,767 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-007-005' recording off
2025-07-24 09:33:28,768 - INFO - Camera 'load-test-camera-loadtest-agent-007-005' created with RTSP stream5
2025-07-24 09:33:29,268 - INFO - Successfully setup agent 'loadtest-agent-007' with 5 cameras
2025-07-24 09:33:29,769 - INFO - Setting up agent 8/10
2025-07-24 09:33:29,769 - INFO - Creating agent 'loadtest-agent-008' (attempt 1)
2025-07-24 09:33:29,839 - INFO - Successfully created agent 'loadtest-agent-008' with ID: deece521-6353-4975-8e1d-3a502d94912d
2025-07-24 09:33:29,839 - INFO - Saved API key to apikeys.txt
2025-07-24 09:33:29,839 - INFO - Saved agent ID to agent_ids.txt
2025-07-24 09:33:29,839 - INFO - Creating camera 'load-test-camera-loadtest-agent-008-001' (attempt 1)
2025-07-24 09:33:29,907 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-008-001' with ID: cc8214be-6f01-4769-be4a-e24ffc2d6f61
2025-07-24 09:33:29,907 - INFO - Setting camera 'cc8214be-6f01-4769-be4a-e24ffc2d6f61' recording off (attempt 1)
2025-07-24 09:33:29,961 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-008-001' recording off
2025-07-24 09:33:29,962 - INFO - Camera 'load-test-camera-loadtest-agent-008-001' created with RTSP stream1
2025-07-24 09:33:30,462 - INFO - Creating camera 'load-test-camera-loadtest-agent-008-002' (attempt 1)
2025-07-24 09:33:30,529 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-008-002' with ID: d30ee442-96a1-4a7f-892c-92d710187544
2025-07-24 09:33:30,529 - INFO - Setting camera 'd30ee442-96a1-4a7f-892c-92d710187544' recording off (attempt 1)
2025-07-24 09:33:30,583 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-008-002' recording off
2025-07-24 09:33:30,583 - INFO - Camera 'load-test-camera-loadtest-agent-008-002' created with RTSP stream2
2025-07-24 09:33:31,083 - INFO - Creating camera 'load-test-camera-loadtest-agent-008-003' (attempt 1)
2025-07-24 09:33:31,169 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-008-003' with ID: fee4e0d8-62f4-498c-9d87-a365879538eb
2025-07-24 09:33:31,169 - INFO - Setting camera 'fee4e0d8-62f4-498c-9d87-a365879538eb' recording off (attempt 1)
2025-07-24 09:33:31,222 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-008-003' recording off
2025-07-24 09:33:31,222 - INFO - Camera 'load-test-camera-loadtest-agent-008-003' created with RTSP stream3
2025-07-24 09:33:31,722 - INFO - Creating camera 'load-test-camera-loadtest-agent-008-004' (attempt 1)
2025-07-24 09:33:31,803 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-008-004' with ID: 27da55ba-23b2-451f-ac74-ec3a19b791df
2025-07-24 09:33:31,803 - INFO - Setting camera '27da55ba-23b2-451f-ac74-ec3a19b791df' recording off (attempt 1)
2025-07-24 09:33:31,857 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-008-004' recording off
2025-07-24 09:33:31,857 - INFO - Camera 'load-test-camera-loadtest-agent-008-004' created with RTSP stream4
2025-07-24 09:33:32,357 - INFO - Creating camera 'load-test-camera-loadtest-agent-008-005' (attempt 1)
2025-07-24 09:33:32,447 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-008-005' with ID: 579541aa-e884-4be3-8171-ff7a56e9c647
2025-07-24 09:33:32,447 - INFO - Setting camera '579541aa-e884-4be3-8171-ff7a56e9c647' recording off (attempt 1)
2025-07-24 09:33:32,498 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-008-005' recording off
2025-07-24 09:33:32,498 - INFO - Camera 'load-test-camera-loadtest-agent-008-005' created with RTSP stream5
2025-07-24 09:33:32,998 - INFO - Successfully setup agent 'loadtest-agent-008' with 5 cameras
2025-07-24 09:33:33,499 - INFO - Setting up agent 9/10
2025-07-24 09:33:33,499 - INFO - Creating agent 'loadtest-agent-009' (attempt 1)
2025-07-24 09:33:33,572 - INFO - Successfully created agent 'loadtest-agent-009' with ID: 8da56d13-b75d-4143-aaaf-870e78d6952b
2025-07-24 09:33:33,572 - INFO - Saved API key to apikeys.txt
2025-07-24 09:33:33,572 - INFO - Saved agent ID to agent_ids.txt
2025-07-24 09:33:33,572 - INFO - Creating camera 'load-test-camera-loadtest-agent-009-001' (attempt 1)
2025-07-24 09:33:33,635 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-009-001' with ID: 3fd00fa0-eecf-4969-8ee8-0e6430f63463
2025-07-24 09:33:33,635 - INFO - Setting camera '3fd00fa0-eecf-4969-8ee8-0e6430f63463' recording off (attempt 1)
2025-07-24 09:33:33,692 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-009-001' recording off
2025-07-24 09:33:33,692 - INFO - Camera 'load-test-camera-loadtest-agent-009-001' created with RTSP stream1
2025-07-24 09:33:34,192 - INFO - Creating camera 'load-test-camera-loadtest-agent-009-002' (attempt 1)
2025-07-24 09:33:34,261 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-009-002' with ID: 3514c393-40be-4ae9-9f1f-3b3b11167cb1
2025-07-24 09:33:34,261 - INFO - Setting camera '3514c393-40be-4ae9-9f1f-3b3b11167cb1' recording off (attempt 1)
2025-07-24 09:33:34,316 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-009-002' recording off
2025-07-24 09:33:34,317 - INFO - Camera 'load-test-camera-loadtest-agent-009-002' created with RTSP stream2
2025-07-24 09:33:34,817 - INFO - Creating camera 'load-test-camera-loadtest-agent-009-003' (attempt 1)
2025-07-24 09:33:34,885 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-009-003' with ID: 79f4de72-fa5c-4b54-8115-697d108deb90
2025-07-24 09:33:34,885 - INFO - Setting camera '79f4de72-fa5c-4b54-8115-697d108deb90' recording off (attempt 1)
2025-07-24 09:33:34,936 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-009-003' recording off
2025-07-24 09:33:34,936 - INFO - Camera 'load-test-camera-loadtest-agent-009-003' created with RTSP stream3
2025-07-24 09:33:35,437 - INFO - Creating camera 'load-test-camera-loadtest-agent-009-004' (attempt 1)
2025-07-24 09:33:35,521 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-009-004' with ID: d398bbe1-d333-40e4-b5af-714eaf6e2e48
2025-07-24 09:33:35,521 - INFO - Setting camera 'd398bbe1-d333-40e4-b5af-714eaf6e2e48' recording off (attempt 1)
2025-07-24 09:33:35,586 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-009-004' recording off
2025-07-24 09:33:35,586 - INFO - Camera 'load-test-camera-loadtest-agent-009-004' created with RTSP stream4
2025-07-24 09:33:36,086 - INFO - Creating camera 'load-test-camera-loadtest-agent-009-005' (attempt 1)
2025-07-24 09:33:36,150 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-009-005' with ID: bd08cf14-a0a5-4184-b1b8-be8b6c8a256a
2025-07-24 09:33:36,151 - INFO - Setting camera 'bd08cf14-a0a5-4184-b1b8-be8b6c8a256a' recording off (attempt 1)
2025-07-24 09:33:36,219 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-009-005' recording off
2025-07-24 09:33:36,219 - INFO - Camera 'load-test-camera-loadtest-agent-009-005' created with RTSP stream5
2025-07-24 09:33:36,720 - INFO - Successfully setup agent 'loadtest-agent-009' with 5 cameras
2025-07-24 09:33:37,221 - INFO - Setting up agent 10/10
2025-07-24 09:33:37,221 - INFO - Creating agent 'loadtest-agent-010' (attempt 1)
2025-07-24 09:33:37,281 - INFO - Successfully created agent 'loadtest-agent-010' with ID: 89dd4c87-f1ba-46d5-92ec-b8b291e7bee4
2025-07-24 09:33:37,281 - INFO - Saved API key to apikeys.txt
2025-07-24 09:33:37,281 - INFO - Saved agent ID to agent_ids.txt
2025-07-24 09:33:37,281 - INFO - Creating camera 'load-test-camera-loadtest-agent-010-001' (attempt 1)
2025-07-24 09:33:37,356 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-010-001' with ID: 157d5d43-bf92-42fd-acc0-d109b02d9395
2025-07-24 09:33:37,356 - INFO - Setting camera '157d5d43-bf92-42fd-acc0-d109b02d9395' recording off (attempt 1)
2025-07-24 09:33:37,409 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-010-001' recording off
2025-07-24 09:33:37,409 - INFO - Camera 'load-test-camera-loadtest-agent-010-001' created with RTSP stream1
2025-07-24 09:33:37,910 - INFO - Creating camera 'load-test-camera-loadtest-agent-010-002' (attempt 1)
2025-07-24 09:33:37,986 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-010-002' with ID: 89c041d5-0516-4b08-a3f3-41a014df4efa
2025-07-24 09:33:37,986 - INFO - Setting camera '89c041d5-0516-4b08-a3f3-41a014df4efa' recording off (attempt 1)
2025-07-24 09:33:38,032 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-010-002' recording off
2025-07-24 09:33:38,033 - INFO - Camera 'load-test-camera-loadtest-agent-010-002' created with RTSP stream2
2025-07-24 09:33:38,533 - INFO - Creating camera 'load-test-camera-loadtest-agent-010-003' (attempt 1)
2025-07-24 09:33:38,617 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-010-003' with ID: 42573d28-d146-4d2b-8d07-563c57e9b0c5
2025-07-24 09:33:38,617 - INFO - Setting camera '42573d28-d146-4d2b-8d07-563c57e9b0c5' recording off (attempt 1)
2025-07-24 09:33:38,674 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-010-003' recording off
2025-07-24 09:33:38,674 - INFO - Camera 'load-test-camera-loadtest-agent-010-003' created with RTSP stream3
2025-07-24 09:33:39,174 - INFO - Creating camera 'load-test-camera-loadtest-agent-010-004' (attempt 1)
2025-07-24 09:33:39,235 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-010-004' with ID: e9f8340e-7f76-44e4-b6ca-56f61ab7485f
2025-07-24 09:33:39,235 - INFO - Setting camera 'e9f8340e-7f76-44e4-b6ca-56f61ab7485f' recording off (attempt 1)
2025-07-24 09:33:39,292 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-010-004' recording off
2025-07-24 09:33:39,292 - INFO - Camera 'load-test-camera-loadtest-agent-010-004' created with RTSP stream4
2025-07-24 09:33:39,793 - INFO - Creating camera 'load-test-camera-loadtest-agent-010-005' (attempt 1)
2025-07-24 09:33:39,869 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-010-005' with ID: 92ac1bc6-4765-4f5c-af4e-c8f6eaff3817
2025-07-24 09:33:39,869 - INFO - Setting camera '92ac1bc6-4765-4f5c-af4e-c8f6eaff3817' recording off (attempt 1)
2025-07-24 09:33:39,924 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-010-005' recording off
2025-07-24 09:33:39,924 - INFO - Camera 'load-test-camera-loadtest-agent-010-005' created with RTSP stream5
2025-07-24 09:33:40,424 - INFO - Successfully setup agent 'loadtest-agent-010' with 5 cameras
2025-07-24 09:33:40,425 - INFO - Setup complete: 10/10 agents created successfully
2025-07-24 09:33:40,425 - INFO - All API keys saved to apikeys.txt
2025-07-24 09:33:40,425 - INFO - All agent IDs saved to agent_ids.txt
2025-07-24 09:33:40,425 - INFO - Load test setup completed successfully!
2025-07-24 15:31:38,411 - INFO - Starting load test setup: 10 agents with 5 cameras each
2025-07-24 15:31:38,411 - INFO - Attempting login (attempt 1)
2025-07-24 15:31:38,710 - INFO - Login successful! Token expires in 3600 seconds
2025-07-24 15:31:38,710 - INFO - Cleared existing apikeys.txt
2025-07-24 15:31:38,710 - INFO - Cleared existing agent_ids.txt
2025-07-24 15:31:38,710 - INFO - Setting up agent 1/10
2025-07-24 15:31:38,710 - INFO - Creating agent 'loadtest-agent-001' (attempt 1)
2025-07-24 15:31:38,896 - INFO - Successfully created agent 'loadtest-agent-001' with ID: fa2bedf5-26be-4e2d-9b72-6a11bd72a302
2025-07-24 15:31:38,897 - INFO - Saved API key to apikeys.txt
2025-07-24 15:31:38,897 - INFO - Saved agent ID to agent_ids.txt
2025-07-24 15:31:38,897 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-001' (attempt 1)
2025-07-24 15:31:38,970 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-001' with ID: 3740dcb9-2b3d-491d-977f-ea36acc34ee5
2025-07-24 15:31:38,970 - INFO - Setting camera '3740dcb9-2b3d-491d-977f-ea36acc34ee5' recording off (attempt 1)
2025-07-24 15:31:39,055 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-001-001' recording off
2025-07-24 15:31:39,055 - INFO - Camera 'load-test-camera-loadtest-agent-001-001' created with RTSP stream1
2025-07-24 15:31:39,555 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-002' (attempt 1)
2025-07-24 15:31:39,619 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-002' with ID: b29c5005-3a7c-46d7-a35f-91ddd99312b3
2025-07-24 15:31:39,619 - INFO - Setting camera 'b29c5005-3a7c-46d7-a35f-91ddd99312b3' recording off (attempt 1)
2025-07-24 15:31:39,673 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-001-002' recording off
2025-07-24 15:31:39,673 - INFO - Camera 'load-test-camera-loadtest-agent-001-002' created with RTSP stream2
2025-07-24 15:31:40,174 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-003' (attempt 1)
2025-07-24 15:31:40,242 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-003' with ID: 8883e9ae-30ef-45bd-bb06-952fde389ac0
2025-07-24 15:31:40,242 - INFO - Setting camera '8883e9ae-30ef-45bd-bb06-952fde389ac0' recording off (attempt 1)
2025-07-24 15:31:40,294 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-001-003' recording off
2025-07-24 15:31:40,294 - INFO - Camera 'load-test-camera-loadtest-agent-001-003' created with RTSP stream3
2025-07-24 15:31:40,795 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-004' (attempt 1)
2025-07-24 15:31:40,866 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-004' with ID: e489575a-5094-4832-ae37-11921ea73659
2025-07-24 15:31:40,866 - INFO - Setting camera 'e489575a-5094-4832-ae37-11921ea73659' recording off (attempt 1)
2025-07-24 15:31:40,919 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-001-004' recording off
2025-07-24 15:31:40,920 - INFO - Camera 'load-test-camera-loadtest-agent-001-004' created with RTSP stream4
2025-07-24 15:31:41,420 - INFO - Creating camera 'load-test-camera-loadtest-agent-001-005' (attempt 1)
2025-07-24 15:31:41,490 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-001-005' with ID: 7943d07c-6cdc-4628-b032-af00454f5934
2025-07-24 15:31:41,490 - INFO - Setting camera '7943d07c-6cdc-4628-b032-af00454f5934' recording off (attempt 1)
2025-07-24 15:31:41,542 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-001-005' recording off
2025-07-24 15:31:41,542 - INFO - Camera 'load-test-camera-loadtest-agent-001-005' created with RTSP stream5
2025-07-24 15:31:42,042 - INFO - Successfully setup agent 'loadtest-agent-001' with 5 cameras
2025-07-24 15:31:42,543 - INFO - Setting up agent 2/10
2025-07-24 15:31:42,543 - INFO - Creating agent 'loadtest-agent-002' (attempt 1)
2025-07-24 15:31:42,610 - INFO - Successfully created agent 'loadtest-agent-002' with ID: 5fbcfa6b-0bfb-4777-839c-8865447f3512
2025-07-24 15:31:42,610 - INFO - Saved API key to apikeys.txt
2025-07-24 15:31:42,610 - INFO - Saved agent ID to agent_ids.txt
2025-07-24 15:31:42,610 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-001' (attempt 1)
2025-07-24 15:31:42,691 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-001' with ID: 0aa9fb62-55ba-4a6a-93ae-8b333601babe
2025-07-24 15:31:42,691 - INFO - Setting camera '0aa9fb62-55ba-4a6a-93ae-8b333601babe' recording off (attempt 1)
2025-07-24 15:31:42,748 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-002-001' recording off
2025-07-24 15:31:42,748 - INFO - Camera 'load-test-camera-loadtest-agent-002-001' created with RTSP stream1
2025-07-24 15:31:43,248 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-002' (attempt 1)
2025-07-24 15:31:43,304 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-002' with ID: 8ee49010-b71c-4162-a0fe-877c69976b0e
2025-07-24 15:31:43,304 - INFO - Setting camera '8ee49010-b71c-4162-a0fe-877c69976b0e' recording off (attempt 1)
2025-07-24 15:31:43,365 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-002-002' recording off
2025-07-24 15:31:43,365 - INFO - Camera 'load-test-camera-loadtest-agent-002-002' created with RTSP stream2
2025-07-24 15:31:43,865 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-003' (attempt 1)
2025-07-24 15:31:43,935 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-003' with ID: ac6be170-ff0f-42e6-9e3d-9c68bcf1ff5f
2025-07-24 15:31:43,935 - INFO - Setting camera 'ac6be170-ff0f-42e6-9e3d-9c68bcf1ff5f' recording off (attempt 1)
2025-07-24 15:31:43,988 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-002-003' recording off
2025-07-24 15:31:43,988 - INFO - Camera 'load-test-camera-loadtest-agent-002-003' created with RTSP stream3
2025-07-24 15:31:44,488 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-004' (attempt 1)
2025-07-24 15:31:44,551 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-004' with ID: 71dec86e-a402-4073-a102-4d4efdd0e7ee
2025-07-24 15:31:44,551 - INFO - Setting camera '71dec86e-a402-4073-a102-4d4efdd0e7ee' recording off (attempt 1)
2025-07-24 15:31:44,602 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-002-004' recording off
2025-07-24 15:31:44,602 - INFO - Camera 'load-test-camera-loadtest-agent-002-004' created with RTSP stream4
2025-07-24 15:31:45,103 - INFO - Creating camera 'load-test-camera-loadtest-agent-002-005' (attempt 1)
2025-07-24 15:31:45,172 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-002-005' with ID: 6f46400a-2cd8-4f8e-9185-7f6851eb2ccc
2025-07-24 15:31:45,172 - INFO - Setting camera '6f46400a-2cd8-4f8e-9185-7f6851eb2ccc' recording off (attempt 1)
2025-07-24 15:31:45,223 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-002-005' recording off
2025-07-24 15:31:45,223 - INFO - Camera 'load-test-camera-loadtest-agent-002-005' created with RTSP stream5
2025-07-24 15:31:45,723 - INFO - Successfully setup agent 'loadtest-agent-002' with 5 cameras
2025-07-24 15:31:46,224 - INFO - Setting up agent 3/10
2025-07-24 15:31:46,224 - INFO - Creating agent 'loadtest-agent-003' (attempt 1)
2025-07-24 15:31:46,283 - INFO - Successfully created agent 'loadtest-agent-003' with ID: 1267090a-aab3-473f-8eb7-9f71fcab753a
2025-07-24 15:31:46,283 - INFO - Saved API key to apikeys.txt
2025-07-24 15:31:46,284 - INFO - Saved agent ID to agent_ids.txt
2025-07-24 15:31:46,284 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-001' (attempt 1)
2025-07-24 15:31:46,346 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-001' with ID: f5f54dcf-13aa-4212-842d-3126154d17ff
2025-07-24 15:31:46,347 - INFO - Setting camera 'f5f54dcf-13aa-4212-842d-3126154d17ff' recording off (attempt 1)
2025-07-24 15:31:46,399 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-003-001' recording off
2025-07-24 15:31:46,399 - INFO - Camera 'load-test-camera-loadtest-agent-003-001' created with RTSP stream1
2025-07-24 15:31:46,900 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-002' (attempt 1)
2025-07-24 15:31:46,966 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-002' with ID: 9e4ed0b9-2886-4e71-bb3f-4464540d3406
2025-07-24 15:31:46,967 - INFO - Setting camera '9e4ed0b9-2886-4e71-bb3f-4464540d3406' recording off (attempt 1)
2025-07-24 15:31:47,023 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-003-002' recording off
2025-07-24 15:31:47,023 - INFO - Camera 'load-test-camera-loadtest-agent-003-002' created with RTSP stream2
2025-07-24 15:31:47,523 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-003' (attempt 1)
2025-07-24 15:31:47,589 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-003' with ID: 75e27ac0-63ef-4dda-b142-6a14dfdaf093
2025-07-24 15:31:47,589 - INFO - Setting camera '75e27ac0-63ef-4dda-b142-6a14dfdaf093' recording off (attempt 1)
2025-07-24 15:31:47,650 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-003-003' recording off
2025-07-24 15:31:47,650 - INFO - Camera 'load-test-camera-loadtest-agent-003-003' created with RTSP stream3
2025-07-24 15:31:48,150 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-004' (attempt 1)
2025-07-24 15:31:48,235 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-004' with ID: 9c3c2560-8d60-4e87-b476-4436d323af79
2025-07-24 15:31:48,235 - INFO - Setting camera '9c3c2560-8d60-4e87-b476-4436d323af79' recording off (attempt 1)
2025-07-24 15:31:48,281 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-003-004' recording off
2025-07-24 15:31:48,281 - INFO - Camera 'load-test-camera-loadtest-agent-003-004' created with RTSP stream4
2025-07-24 15:31:48,782 - INFO - Creating camera 'load-test-camera-loadtest-agent-003-005' (attempt 1)
2025-07-24 15:31:48,844 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-003-005' with ID: 68dca5e7-6222-4e6a-ae9e-e04b6c771b25
2025-07-24 15:31:48,844 - INFO - Setting camera '68dca5e7-6222-4e6a-ae9e-e04b6c771b25' recording off (attempt 1)
2025-07-24 15:31:48,897 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-003-005' recording off
2025-07-24 15:31:48,898 - INFO - Camera 'load-test-camera-loadtest-agent-003-005' created with RTSP stream5
2025-07-24 15:31:49,398 - INFO - Successfully setup agent 'loadtest-agent-003' with 5 cameras
2025-07-24 15:31:49,899 - INFO - Setting up agent 4/10
2025-07-24 15:31:49,899 - INFO - Creating agent 'loadtest-agent-004' (attempt 1)
2025-07-24 15:31:49,956 - INFO - Successfully created agent 'loadtest-agent-004' with ID: 87f9db9e-5159-45ff-ac3c-ee93fac7f316
2025-07-24 15:31:49,957 - INFO - Saved API key to apikeys.txt
2025-07-24 15:31:49,957 - INFO - Saved agent ID to agent_ids.txt
2025-07-24 15:31:49,957 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-001' (attempt 1)
2025-07-24 15:31:50,016 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-001' with ID: 8f0add33-97b6-4eee-a9bd-e972c114de10
2025-07-24 15:31:50,016 - INFO - Setting camera '8f0add33-97b6-4eee-a9bd-e972c114de10' recording off (attempt 1)
2025-07-24 15:31:50,064 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-004-001' recording off
2025-07-24 15:31:50,064 - INFO - Camera 'load-test-camera-loadtest-agent-004-001' created with RTSP stream1
2025-07-24 15:31:50,564 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-002' (attempt 1)
2025-07-24 15:31:50,626 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-002' with ID: e4c5cece-8a53-4881-9add-08c5c289c227
2025-07-24 15:31:50,627 - INFO - Setting camera 'e4c5cece-8a53-4881-9add-08c5c289c227' recording off (attempt 1)
2025-07-24 15:31:50,684 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-004-002' recording off
2025-07-24 15:31:50,684 - INFO - Camera 'load-test-camera-loadtest-agent-004-002' created with RTSP stream2
2025-07-24 15:31:51,184 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-003' (attempt 1)
2025-07-24 15:31:51,247 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-003' with ID: a07fd883-5fb2-4ccd-b7e6-6782972eb56b
2025-07-24 15:31:51,247 - INFO - Setting camera 'a07fd883-5fb2-4ccd-b7e6-6782972eb56b' recording off (attempt 1)
2025-07-24 15:31:51,295 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-004-003' recording off
2025-07-24 15:31:51,295 - INFO - Camera 'load-test-camera-loadtest-agent-004-003' created with RTSP stream3
2025-07-24 15:31:51,795 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-004' (attempt 1)
2025-07-24 15:31:51,863 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-004' with ID: ada02a15-cc8e-4a9c-ae91-3e731774bda4
2025-07-24 15:31:51,863 - INFO - Setting camera 'ada02a15-cc8e-4a9c-ae91-3e731774bda4' recording off (attempt 1)
2025-07-24 15:31:51,909 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-004-004' recording off
2025-07-24 15:31:51,909 - INFO - Camera 'load-test-camera-loadtest-agent-004-004' created with RTSP stream4
2025-07-24 15:31:52,409 - INFO - Creating camera 'load-test-camera-loadtest-agent-004-005' (attempt 1)
2025-07-24 15:31:52,477 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-004-005' with ID: 693180f4-850d-41f5-89e1-4823a28c4f94
2025-07-24 15:31:52,477 - INFO - Setting camera '693180f4-850d-41f5-89e1-4823a28c4f94' recording off (attempt 1)
2025-07-24 15:31:52,532 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-004-005' recording off
2025-07-24 15:31:52,532 - INFO - Camera 'load-test-camera-loadtest-agent-004-005' created with RTSP stream5
2025-07-24 15:31:53,032 - INFO - Successfully setup agent 'loadtest-agent-004' with 5 cameras
2025-07-24 15:31:53,533 - INFO - Setting up agent 5/10
2025-07-24 15:31:53,533 - INFO - Creating agent 'loadtest-agent-005' (attempt 1)
2025-07-24 15:31:53,587 - INFO - Successfully created agent 'loadtest-agent-005' with ID: 90378d43-9546-4f4f-857f-6deab86341e7
2025-07-24 15:31:53,588 - INFO - Saved API key to apikeys.txt
2025-07-24 15:31:53,588 - INFO - Saved agent ID to agent_ids.txt
2025-07-24 15:31:53,588 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-001' (attempt 1)
2025-07-24 15:31:53,650 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-001' with ID: 38bdab01-2dab-474c-b18a-d104f69e8e28
2025-07-24 15:31:53,650 - INFO - Setting camera '38bdab01-2dab-474c-b18a-d104f69e8e28' recording off (attempt 1)
2025-07-24 15:31:53,696 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-005-001' recording off
2025-07-24 15:31:53,696 - INFO - Camera 'load-test-camera-loadtest-agent-005-001' created with RTSP stream1
2025-07-24 15:31:54,196 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-002' (attempt 1)
2025-07-24 15:31:54,253 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-002' with ID: d34b5e4c-7341-423d-b31d-1f681aa88fe5
2025-07-24 15:31:54,253 - INFO - Setting camera 'd34b5e4c-7341-423d-b31d-1f681aa88fe5' recording off (attempt 1)
2025-07-24 15:31:54,294 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-005-002' recording off
2025-07-24 15:31:54,294 - INFO - Camera 'load-test-camera-loadtest-agent-005-002' created with RTSP stream2
2025-07-24 15:31:54,794 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-003' (attempt 1)
2025-07-24 15:31:54,857 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-003' with ID: ae3701ec-97dd-42bc-a100-41620c0a418b
2025-07-24 15:31:54,858 - INFO - Setting camera 'ae3701ec-97dd-42bc-a100-41620c0a418b' recording off (attempt 1)
2025-07-24 15:31:54,903 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-005-003' recording off
2025-07-24 15:31:54,903 - INFO - Camera 'load-test-camera-loadtest-agent-005-003' created with RTSP stream3
2025-07-24 15:31:55,404 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-004' (attempt 1)
2025-07-24 15:31:55,470 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-004' with ID: b3458b74-dbbf-4513-bb64-68f35c2d3dfd
2025-07-24 15:31:55,470 - INFO - Setting camera 'b3458b74-dbbf-4513-bb64-68f35c2d3dfd' recording off (attempt 1)
2025-07-24 15:31:55,519 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-005-004' recording off
2025-07-24 15:31:55,519 - INFO - Camera 'load-test-camera-loadtest-agent-005-004' created with RTSP stream4
2025-07-24 15:31:56,020 - INFO - Creating camera 'load-test-camera-loadtest-agent-005-005' (attempt 1)
2025-07-24 15:31:56,076 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-005-005' with ID: 1ee617ba-969a-4c4a-91bc-f2dc1163bd5e
2025-07-24 15:31:56,076 - INFO - Setting camera '1ee617ba-969a-4c4a-91bc-f2dc1163bd5e' recording off (attempt 1)
2025-07-24 15:31:56,119 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-005-005' recording off
2025-07-24 15:31:56,119 - INFO - Camera 'load-test-camera-loadtest-agent-005-005' created with RTSP stream5
2025-07-24 15:31:56,620 - INFO - Successfully setup agent 'loadtest-agent-005' with 5 cameras
2025-07-24 15:31:57,121 - INFO - Setting up agent 6/10
2025-07-24 15:31:57,121 - INFO - Creating agent 'loadtest-agent-006' (attempt 1)
2025-07-24 15:31:57,173 - INFO - Successfully created agent 'loadtest-agent-006' with ID: fd06adbc-a329-48ae-8ee3-8a73637e95c6
2025-07-24 15:31:57,174 - INFO - Saved API key to apikeys.txt
2025-07-24 15:31:57,174 - INFO - Saved agent ID to agent_ids.txt
2025-07-24 15:31:57,174 - INFO - Creating camera 'load-test-camera-loadtest-agent-006-001' (attempt 1)
2025-07-24 15:31:57,232 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-006-001' with ID: 540bc8f8-1daf-468a-93e3-63d6953f0d6e
2025-07-24 15:31:57,232 - INFO - Setting camera '540bc8f8-1daf-468a-93e3-63d6953f0d6e' recording off (attempt 1)
2025-07-24 15:31:57,281 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-006-001' recording off
2025-07-24 15:31:57,281 - INFO - Camera 'load-test-camera-loadtest-agent-006-001' created with RTSP stream1
2025-07-24 15:31:57,782 - INFO - Creating camera 'load-test-camera-loadtest-agent-006-002' (attempt 1)
2025-07-24 15:31:57,847 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-006-002' with ID: f4aff6d4-b1f0-445b-83bd-9c6c445dbd0b
2025-07-24 15:31:57,847 - INFO - Setting camera 'f4aff6d4-b1f0-445b-83bd-9c6c445dbd0b' recording off (attempt 1)
2025-07-24 15:31:57,899 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-006-002' recording off
2025-07-24 15:31:57,899 - INFO - Camera 'load-test-camera-loadtest-agent-006-002' created with RTSP stream2
2025-07-24 15:31:58,400 - INFO - Creating camera 'load-test-camera-loadtest-agent-006-003' (attempt 1)
2025-07-24 15:31:58,536 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-006-003' with ID: ab4defa1-9f26-44e9-aaf2-ffc825d4c0fd
2025-07-24 15:31:58,536 - INFO - Setting camera 'ab4defa1-9f26-44e9-aaf2-ffc825d4c0fd' recording off (attempt 1)
2025-07-24 15:31:58,593 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-006-003' recording off
2025-07-24 15:31:58,593 - INFO - Camera 'load-test-camera-loadtest-agent-006-003' created with RTSP stream3
2025-07-24 15:31:59,094 - INFO - Creating camera 'load-test-camera-loadtest-agent-006-004' (attempt 1)
2025-07-24 15:31:59,161 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-006-004' with ID: 9646a0e1-ea9f-4c84-90ca-253ad2a9ce44
2025-07-24 15:31:59,161 - INFO - Setting camera '9646a0e1-ea9f-4c84-90ca-253ad2a9ce44' recording off (attempt 1)
2025-07-24 15:31:59,220 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-006-004' recording off
2025-07-24 15:31:59,220 - INFO - Camera 'load-test-camera-loadtest-agent-006-004' created with RTSP stream4
2025-07-24 15:31:59,721 - INFO - Creating camera 'load-test-camera-loadtest-agent-006-005' (attempt 1)
2025-07-24 15:31:59,785 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-006-005' with ID: 01493454-e4cb-4413-afce-1a69f329ce98
2025-07-24 15:31:59,785 - INFO - Setting camera '01493454-e4cb-4413-afce-1a69f329ce98' recording off (attempt 1)
2025-07-24 15:31:59,837 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-006-005' recording off
2025-07-24 15:31:59,837 - INFO - Camera 'load-test-camera-loadtest-agent-006-005' created with RTSP stream5
2025-07-24 15:32:00,338 - INFO - Successfully setup agent 'loadtest-agent-006' with 5 cameras
2025-07-24 15:32:00,839 - INFO - Setting up agent 7/10
2025-07-24 15:32:00,839 - INFO - Creating agent 'loadtest-agent-007' (attempt 1)
2025-07-24 15:32:00,901 - INFO - Successfully created agent 'loadtest-agent-007' with ID: fc152816-bcb6-469b-bcd2-198f08be8b3d
2025-07-24 15:32:00,901 - INFO - Saved API key to apikeys.txt
2025-07-24 15:32:00,901 - INFO - Saved agent ID to agent_ids.txt
2025-07-24 15:32:00,901 - INFO - Creating camera 'load-test-camera-loadtest-agent-007-001' (attempt 1)
2025-07-24 15:32:00,970 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-007-001' with ID: 0c00ba09-9def-4fc5-9e35-f6a706b2cc7c
2025-07-24 15:32:00,970 - INFO - Setting camera '0c00ba09-9def-4fc5-9e35-f6a706b2cc7c' recording off (attempt 1)
2025-07-24 15:32:01,019 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-007-001' recording off
2025-07-24 15:32:01,019 - INFO - Camera 'load-test-camera-loadtest-agent-007-001' created with RTSP stream1
2025-07-24 15:32:01,520 - INFO - Creating camera 'load-test-camera-loadtest-agent-007-002' (attempt 1)
2025-07-24 15:32:01,585 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-007-002' with ID: 32b87895-43e1-4848-b718-68af35758868
2025-07-24 15:32:01,585 - INFO - Setting camera '32b87895-43e1-4848-b718-68af35758868' recording off (attempt 1)
2025-07-24 15:32:01,630 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-007-002' recording off
2025-07-24 15:32:01,630 - INFO - Camera 'load-test-camera-loadtest-agent-007-002' created with RTSP stream2
2025-07-24 15:32:02,130 - INFO - Creating camera 'load-test-camera-loadtest-agent-007-003' (attempt 1)
2025-07-24 15:32:02,201 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-007-003' with ID: 3cb6292e-0912-4270-b59d-1fea8b8d30aa
2025-07-24 15:32:02,201 - INFO - Setting camera '3cb6292e-0912-4270-b59d-1fea8b8d30aa' recording off (attempt 1)
2025-07-24 15:32:02,263 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-007-003' recording off
2025-07-24 15:32:02,263 - INFO - Camera 'load-test-camera-loadtest-agent-007-003' created with RTSP stream3
2025-07-24 15:32:02,763 - INFO - Creating camera 'load-test-camera-loadtest-agent-007-004' (attempt 1)
2025-07-24 15:32:02,815 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-007-004' with ID: d94b5e2f-9c51-4860-a798-205e6d74b1ca
2025-07-24 15:32:02,815 - INFO - Setting camera 'd94b5e2f-9c51-4860-a798-205e6d74b1ca' recording off (attempt 1)
2025-07-24 15:32:02,876 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-007-004' recording off
2025-07-24 15:32:02,876 - INFO - Camera 'load-test-camera-loadtest-agent-007-004' created with RTSP stream4
2025-07-24 15:32:03,377 - INFO - Creating camera 'load-test-camera-loadtest-agent-007-005' (attempt 1)
2025-07-24 15:32:03,456 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-007-005' with ID: e5d4162b-89ea-49ed-88d7-91e80ae7f1f3
2025-07-24 15:32:03,456 - INFO - Setting camera 'e5d4162b-89ea-49ed-88d7-91e80ae7f1f3' recording off (attempt 1)
2025-07-24 15:32:03,507 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-007-005' recording off
2025-07-24 15:32:03,507 - INFO - Camera 'load-test-camera-loadtest-agent-007-005' created with RTSP stream5
2025-07-24 15:32:04,008 - INFO - Successfully setup agent 'loadtest-agent-007' with 5 cameras
2025-07-24 15:32:04,509 - INFO - Setting up agent 8/10
2025-07-24 15:32:04,509 - INFO - Creating agent 'loadtest-agent-008' (attempt 1)
2025-07-24 15:32:04,572 - INFO - Successfully created agent 'loadtest-agent-008' with ID: 4cfc5643-e81c-4f60-8c30-571000a20c1f
2025-07-24 15:32:04,572 - INFO - Saved API key to apikeys.txt
2025-07-24 15:32:04,572 - INFO - Saved agent ID to agent_ids.txt
2025-07-24 15:32:04,573 - INFO - Creating camera 'load-test-camera-loadtest-agent-008-001' (attempt 1)
2025-07-24 15:32:04,636 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-008-001' with ID: 922d38ca-a63a-4d55-92eb-48fe99eedc6a
2025-07-24 15:32:04,636 - INFO - Setting camera '922d38ca-a63a-4d55-92eb-48fe99eedc6a' recording off (attempt 1)
2025-07-24 15:32:04,714 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-008-001' recording off
2025-07-24 15:32:04,714 - INFO - Camera 'load-test-camera-loadtest-agent-008-001' created with RTSP stream1
2025-07-24 15:32:05,215 - INFO - Creating camera 'load-test-camera-loadtest-agent-008-002' (attempt 1)
2025-07-24 15:32:05,276 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-008-002' with ID: 16be5060-2455-48c4-80c5-8736849d9398
2025-07-24 15:32:05,277 - INFO - Setting camera '16be5060-2455-48c4-80c5-8736849d9398' recording off (attempt 1)
2025-07-24 15:32:05,330 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-008-002' recording off
2025-07-24 15:32:05,330 - INFO - Camera 'load-test-camera-loadtest-agent-008-002' created with RTSP stream2
2025-07-24 15:32:05,830 - INFO - Creating camera 'load-test-camera-loadtest-agent-008-003' (attempt 1)
2025-07-24 15:32:05,906 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-008-003' with ID: 0f8621b8-e076-4d29-802b-8948314fda8d
2025-07-24 15:32:05,906 - INFO - Setting camera '0f8621b8-e076-4d29-802b-8948314fda8d' recording off (attempt 1)
2025-07-24 15:32:05,979 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-008-003' recording off
2025-07-24 15:32:05,979 - INFO - Camera 'load-test-camera-loadtest-agent-008-003' created with RTSP stream3
2025-07-24 15:32:06,480 - INFO - Creating camera 'load-test-camera-loadtest-agent-008-004' (attempt 1)
2025-07-24 15:32:06,557 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-008-004' with ID: c85514a5-c10e-419e-bc2d-83be02039705
2025-07-24 15:32:06,558 - INFO - Setting camera 'c85514a5-c10e-419e-bc2d-83be02039705' recording off (attempt 1)
2025-07-24 15:32:06,613 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-008-004' recording off
2025-07-24 15:32:06,613 - INFO - Camera 'load-test-camera-loadtest-agent-008-004' created with RTSP stream4
2025-07-24 15:32:07,113 - INFO - Creating camera 'load-test-camera-loadtest-agent-008-005' (attempt 1)
2025-07-24 15:32:07,178 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-008-005' with ID: a2567ce2-f7c1-4a16-96a6-a073ac36a640
2025-07-24 15:32:07,178 - INFO - Setting camera 'a2567ce2-f7c1-4a16-96a6-a073ac36a640' recording off (attempt 1)
2025-07-24 15:32:07,244 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-008-005' recording off
2025-07-24 15:32:07,244 - INFO - Camera 'load-test-camera-loadtest-agent-008-005' created with RTSP stream5
2025-07-24 15:32:07,745 - INFO - Successfully setup agent 'loadtest-agent-008' with 5 cameras
2025-07-24 15:32:08,245 - INFO - Setting up agent 9/10
2025-07-24 15:32:08,245 - INFO - Creating agent 'loadtest-agent-009' (attempt 1)
2025-07-24 15:32:08,305 - INFO - Successfully created agent 'loadtest-agent-009' with ID: 58cb55b3-998e-458a-9186-8bad797b6d6c
2025-07-24 15:32:08,305 - INFO - Saved API key to apikeys.txt
2025-07-24 15:32:08,305 - INFO - Saved agent ID to agent_ids.txt
2025-07-24 15:32:08,306 - INFO - Creating camera 'load-test-camera-loadtest-agent-009-001' (attempt 1)
2025-07-24 15:32:08,372 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-009-001' with ID: 673ea449-441c-43b2-b461-88acedc341e7
2025-07-24 15:32:08,372 - INFO - Setting camera '673ea449-441c-43b2-b461-88acedc341e7' recording off (attempt 1)
2025-07-24 15:32:08,426 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-009-001' recording off
2025-07-24 15:32:08,427 - INFO - Camera 'load-test-camera-loadtest-agent-009-001' created with RTSP stream1
2025-07-24 15:32:08,927 - INFO - Creating camera 'load-test-camera-loadtest-agent-009-002' (attempt 1)
2025-07-24 15:32:08,992 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-009-002' with ID: b3ff406f-ee5f-4526-a52e-d00fee910540
2025-07-24 15:32:08,992 - INFO - Setting camera 'b3ff406f-ee5f-4526-a52e-d00fee910540' recording off (attempt 1)
2025-07-24 15:32:09,047 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-009-002' recording off
2025-07-24 15:32:09,047 - INFO - Camera 'load-test-camera-loadtest-agent-009-002' created with RTSP stream2
2025-07-24 15:32:09,548 - INFO - Creating camera 'load-test-camera-loadtest-agent-009-003' (attempt 1)
2025-07-24 15:32:09,611 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-009-003' with ID: 38d4bc2b-0a9d-4dbf-99d7-be96d41d6964
2025-07-24 15:32:09,611 - INFO - Setting camera '38d4bc2b-0a9d-4dbf-99d7-be96d41d6964' recording off (attempt 1)
2025-07-24 15:32:09,677 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-009-003' recording off
2025-07-24 15:32:09,677 - INFO - Camera 'load-test-camera-loadtest-agent-009-003' created with RTSP stream3
2025-07-24 15:32:10,178 - INFO - Creating camera 'load-test-camera-loadtest-agent-009-004' (attempt 1)
2025-07-24 15:32:10,244 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-009-004' with ID: cf29a24f-88e9-44dd-8b08-26db806712f4
2025-07-24 15:32:10,244 - INFO - Setting camera 'cf29a24f-88e9-44dd-8b08-26db806712f4' recording off (attempt 1)
2025-07-24 15:32:10,301 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-009-004' recording off
2025-07-24 15:32:10,301 - INFO - Camera 'load-test-camera-loadtest-agent-009-004' created with RTSP stream4
2025-07-24 15:32:10,802 - INFO - Creating camera 'load-test-camera-loadtest-agent-009-005' (attempt 1)
2025-07-24 15:32:10,866 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-009-005' with ID: c3d76985-979e-4093-978f-1c764f2479a7
2025-07-24 15:32:10,866 - INFO - Setting camera 'c3d76985-979e-4093-978f-1c764f2479a7' recording off (attempt 1)
2025-07-24 15:32:10,920 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-009-005' recording off
2025-07-24 15:32:10,920 - INFO - Camera 'load-test-camera-loadtest-agent-009-005' created with RTSP stream5
2025-07-24 15:32:11,421 - INFO - Successfully setup agent 'loadtest-agent-009' with 5 cameras
2025-07-24 15:32:11,922 - INFO - Setting up agent 10/10
2025-07-24 15:32:11,922 - INFO - Creating agent 'loadtest-agent-010' (attempt 1)
2025-07-24 15:32:11,991 - INFO - Successfully created agent 'loadtest-agent-010' with ID: 93b047ee-c7d3-4dfa-9c91-2b7b506a0a82
2025-07-24 15:32:11,991 - INFO - Saved API key to apikeys.txt
2025-07-24 15:32:11,991 - INFO - Saved agent ID to agent_ids.txt
2025-07-24 15:32:11,991 - INFO - Creating camera 'load-test-camera-loadtest-agent-010-001' (attempt 1)
2025-07-24 15:32:12,063 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-010-001' with ID: 5558bbdb-866e-49cf-b3c8-93120d5ad995
2025-07-24 15:32:12,063 - INFO - Setting camera '5558bbdb-866e-49cf-b3c8-93120d5ad995' recording off (attempt 1)
2025-07-24 15:32:12,115 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-010-001' recording off
2025-07-24 15:32:12,115 - INFO - Camera 'load-test-camera-loadtest-agent-010-001' created with RTSP stream1
2025-07-24 15:32:12,615 - INFO - Creating camera 'load-test-camera-loadtest-agent-010-002' (attempt 1)
2025-07-24 15:32:12,694 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-010-002' with ID: 9ee998a2-7560-4443-a845-5e379f8d2dba
2025-07-24 15:32:12,694 - INFO - Setting camera '9ee998a2-7560-4443-a845-5e379f8d2dba' recording off (attempt 1)
2025-07-24 15:32:12,759 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-010-002' recording off
2025-07-24 15:32:12,759 - INFO - Camera 'load-test-camera-loadtest-agent-010-002' created with RTSP stream2
2025-07-24 15:32:13,260 - INFO - Creating camera 'load-test-camera-loadtest-agent-010-003' (attempt 1)
2025-07-24 15:32:13,335 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-010-003' with ID: 132567a0-fba6-469d-b2d0-496c7fd8bace
2025-07-24 15:32:13,335 - INFO - Setting camera '132567a0-fba6-469d-b2d0-496c7fd8bace' recording off (attempt 1)
2025-07-24 15:32:13,392 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-010-003' recording off
2025-07-24 15:32:13,392 - INFO - Camera 'load-test-camera-loadtest-agent-010-003' created with RTSP stream3
2025-07-24 15:32:13,893 - INFO - Creating camera 'load-test-camera-loadtest-agent-010-004' (attempt 1)
2025-07-24 15:32:13,959 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-010-004' with ID: ec86b0f3-b8bd-408b-9d29-0adf614b0233
2025-07-24 15:32:13,959 - INFO - Setting camera 'ec86b0f3-b8bd-408b-9d29-0adf614b0233' recording off (attempt 1)
2025-07-24 15:32:14,015 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-010-004' recording off
2025-07-24 15:32:14,015 - INFO - Camera 'load-test-camera-loadtest-agent-010-004' created with RTSP stream4
2025-07-24 15:32:14,515 - INFO - Creating camera 'load-test-camera-loadtest-agent-010-005' (attempt 1)
2025-07-24 15:32:14,581 - INFO - Successfully created camera 'load-test-camera-loadtest-agent-010-005' with ID: 96bba68e-f958-4e07-a507-add844f5c8c5
2025-07-24 15:32:14,581 - INFO - Setting camera '96bba68e-f958-4e07-a507-add844f5c8c5' recording off (attempt 1)
2025-07-24 15:32:14,646 - INFO - Successfully set camera 'load-test-camera-loadtest-agent-010-005' recording off
2025-07-24 15:32:14,646 - INFO - Camera 'load-test-camera-loadtest-agent-010-005' created with RTSP stream5
2025-07-24 15:32:15,147 - INFO - Successfully setup agent 'loadtest-agent-010' with 5 cameras
2025-07-24 15:32:15,147 - INFO - Setup complete: 10/10 agents created successfully
2025-07-24 15:32:15,147 - INFO - All API keys saved to apikeys.txt
2025-07-24 15:32:15,147 - INFO - All agent IDs saved to agent_ids.txt
2025-07-24 15:32:15,147 - INFO - Load test setup completed successfully!
